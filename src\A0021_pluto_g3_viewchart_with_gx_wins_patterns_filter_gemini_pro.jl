### A Pluto.jl notebook ###
# v0.20.4

using Markdown
using InteractiveUtils

# This Pluto notebook uses @bind for interactivity. When running this notebook outside of Pluto, the following 'mock version' of @bind gives bound variables a default value (instead of an error).
macro bind(def, element)
    #! format: off
    quote
        local iv = try Base.loaded_modules[Base.PkgId(Base.UUID("6e696c72-**************-42206c756150"), "AbstractPlutoDingetjes")].Bonds.initial_value catch; b -> missing; end
        local el = $(esc(element))
        global $(esc(def)) = Core.applicable(Base.get, el) ? Base.get(el) : iv(el)
        el
    end
    #! format: on
end

# ╔═╡ d1634666-de2d-4746-b0aa-1dd8b94193a9
begin
    using PlutoUI, Random, CSV, DataFrames, Combinatorics
    using Plots, StatsBase, Dates
    using DynamicAxisWarping # 或 using DTW
end

# ╔═╡ 6d686588-4878-4ad6-a88b-d2fa155f9260
using StaticArrays

# ╔═╡ e9e3bd04-3446-4db2-a6a9-9c557b120c90
using Serialization

# ╔═╡ 110c2e45-3000-4c32-acdf-75f0c703e2e3
using ThreadSafeDicts

# ╔═╡ 207ecbca-2bc5-445e-b603-f3c717e83e45
using OrderedCollections

# ╔═╡ d871dcc0-fe0f-11ef-1d52-277e2f226a7a
begin
	# using Markdown
	# using InteractiveUtils
end

# ╔═╡ 5e9cb45f-5cfe-4139-8ca4-7b8a2d7ec8a3
cd("..")


# ╔═╡ 4662b0f9-f265-4d36-a42b-52a4e3006e27
pwd()

# ╔═╡ 86d63da4-aa17-49ff-8bdb-57b1a42265c6
begin
    function save_combinations(filename::String, combinations::Vector{Vector{Int64}})
        open(filename, "w") do io
            serialize(io, combinations)
        end
    end

    function load_combinations(filename::String)
        open(filename, "r") do io
            deserialize(io)
        end
    end
end

# ╔═╡ 80033eaa-4e5a-47e7-914b-9a4c5c3e2d35
begin
    struct LotteryData
        raw_data::DataFrame
        numbers::Vector{Vector{Int}}
        nx::Matrix{Int}
        dates::Vector{String}
    end
	
	
    function load_lottery_data(file_path::String)::LotteryData
        @info "Loading data from $(file_path)"
        df = CSV.File(file_path, header=false) |> DataFrame
        numbers = [Vector{Int16}(row[2:end]) for row in eachrow(df)]
        nx = reshape(vcat(numbers...), 5, :) |> transpose |> collect
        dates = string.(df[!, 1])
        @info "Data loaded successfully. Total records: $(nrow(df))"
        LotteryData(df, numbers, nx, dates)
    end

end

# ╔═╡ 9286d50a-7d0b-479a-8ed2-46d9278e78c9
# Load data only once, outside reactive cells.
begin
    game = "fan5" # tw539 or fan5
    file_path = "./data/$(game).csv"
    const data = load_lottery_data(file_path)  #  use const for top-level data
    const data_s = data.numbers
    const nx = data.nx
    const data_rows = length(data.numbers)
end

# ╔═╡ f271e3db-625c-4d9e-9178-317b16b9de7d
data.numbers

# ╔═╡ d1dc9c7c-5019-442c-8128-c63f111aba4d
begin
    function sms(wins::Vector{Float64}, window::Int)::Vector{Float64}
        n = length(wins)
        if window > n
            return zeros(Float64, n)
        end
        cumsum_wins = cumsum(wins)
        result = zeros(Float64, n)
        for i in window:n
            result[i] = cumsum_wins[i] - (i > window ? cumsum_wins[i - window] : 0.0)
        end
        return round.(result, digits=1)
    end

	function smsint16(wins::Vector{Int}, window::Int)::Vector{Int}
	    n = length(wins)
	    result = zeros(Int8, n)
	    for i in window:n
	        slice = wins[i-window+1:i]
	        result[i] = sum(slice)
	    end
	    return result
	end
	
    # IMPORTANT: Use a struct to hold cached results *and* the last gx.
    struct CalculationCache
        last_gx::Vector{Int}  # Store the gx used for the last calculation.
        price::Vector{Float64}
        cum7::Vector{Int}
        cum14::Vector{Int}
        cum21::Vector{Int}
        gx_wins::Vector{Int}
    end

	function netstar2wins(gx::Vector{Int}, data::Vector{Vector{Int}}, prob::Float64)::Vector{Float64}
		gxstar2wins = star2wins(gx, data)
		return gxstar2wins .- (prob * binomial(length(gx),2))
	end
	
    # Create a cache *outside* the main plotting function.  Initialize last_gx.
    cache = Ref{CalculationCache}(CalculationCache([], [], [], [], [], []))
	cache2 = Ref{CalculationCache}(CalculationCache([], [], [], [], [], []))


    function _calculate_prices!(gx, cache::Ref{CalculationCache})
        prob = 1/ 7
        gx_length = length(gx)
        gx_wins = count.(x -> x in gx, data_s)  # Counts occurrences in ALL data_s
        netwins = gx_wins .- (prob * gx_length)
		
		gxstar2wins = binomial.(gx_wins,2)
		star2price = gxstar2wins .- (1/70 * binomial(length(gx),2))
		
        # cum7 = smsint16(netwins, 7)
        # cum14 = smsint16(netwins, 14)
        # cum21 = smsint16(netwins, 21)

	    cum7 = smsint16(gx_wins, 7) .- gx_length
	    cum14 = smsint16(gx_wins, 14) .- (gx_length * 2)
	    cum21 = smsint16(gx_wins, 21) .- (gx_length * 3)
			
        price = round.(cumsum(gx_wins .- (1/ 6 * gx_length)), digits=3)
		# price = star2price
        # Update the cache, including last_gx
        cache[] = CalculationCache(copy(gx), price, cum7, cum14, cum21, gx_wins) #CRITICAL: copy(gx)
        return cache[]
    end

    # Initial calculation (outside reactive cell, but now we need gx)
    # This is best done *after* gx is defined. See below.

	# --------------------------------------------------
	# Make sure you have imported the Statistics package
	using Statistics
	
	# Assuming CalculationCache struct is defined elsewhere, e.g.:
	# struct CalculationCache
	#     last_gx::Vector{Any} # Use appropriate concrete type if possible
	#     price::Vector{Float64} # This will now hold the standardized price
	#     cum7::Vector{Int16}
	#     cum14::Vector{Int16}
	#     cum21::Vector{Int16}
	#     gx_wins::Vector{Int} # Use appropriate concrete type if possible
	# end
	
	# Assuming data_s and smsint16 are defined elsewhere
	
	"""
	Calculates cumulative wins, standardized price, and updates the cache.
	"""
	function calculate_prices_star2!(gx, cache2::Ref{CalculationCache})
	    prob = 1 / 53
	    gx_length = length(gx)
	
	    # --- Calculations from original function ---
	    gx_wins = count.(x -> x in gx, data_s)  # Counts occurrences in ALL data_s
		gx_star2_wins = binomial.(gx_wins,2)
	    # netwins = gx_wins .- (prob * gx_length) # Original calculation, commented out in input
	
	    # Using the second cumulative sum calculation method from input
	    cum7 = smsint16(gx_wins, 7) .- gx_length
	    cum14 = smsint16(gx_wins, 14) .- (gx_length * 2)
	    cum21 = smsint16(gx_wins, 21) .- (gx_length * 3)
	
	    # Calculate the raw, unstandardized price (cumulative sum)
	    # Perform calculations before rounding for better precision
	    raw_price = cumsum(gx_star2_wins .- (prob * binomial(gx_length,2) *  0.8))
	
	    # --- Standardization of the price vector ---
	    standardized_price = Vector{Float64}(undef, length(raw_price)) # Pre-allocate
	
	    if isempty(raw_price)
	        # Handle empty price vector case - standardized price is also empty
	        standardized_price = Float64[] # Assign an empty vector of the correct type
	    else
	        mean_price = mean(raw_price)
	        std_dev_price = std(raw_price)
	
	        if isapprox(std_dev_price, 0.0; atol=1e-8)
	            # If standard deviation is zero (all prices are the same),
	            # all standardized values are zero. Avoid division by zero.
	            fill!(standardized_price, 0.0)
	        else
	            # Apply Z-score standardization: (value - mean) / std_dev
	            standardized_price .= (raw_price .- mean_price) ./ std_dev_price
	        end
	
	        # Optional: Round the standardized price AFTER calculation if desired
	        # standardized_price .= round.(standardized_price, digits=3)
	        # Note: Standardized values are often not rounded unless needed for display.
	    end
	    # --- End Standardization ---
	
	
	    # Update the cache with the STANDARDIZED price
	    # CRITICAL: copy(gx) is maintained as in the original code
	    cache2[] = CalculationCache(copy(gx), standardized_price, cum7, cum14, cum21, gx_wins)
	
	    return cache2[]
	end
	
	# Example Usage (requires definitions for CalculationCache, data_s, smsint16)
	# Define dummy data/functions for demonstration if needed:
	# struct CalculationCache ... end
	# data_s = [[1,2,3], [1,4,5], [2,6,7], [1,3,8], [9,10,1]]
	# smsint16(data, window) = # some implementation, maybe using rolling window functions
	# cache = Ref{CalculationCache}()
	# gx_example = [1, 2]
	# result_cache = calculate_prices!(gx_example, cache)
	# println("Standardized Price: ", result_cache.price)
	# println("Mean: ", mean(result_cache.price)) # Should be close to 0
	# println("Std Dev: ", std(result_cache.price))   # Should be close to 1
		
end

# ╔═╡ f0b331af-5991-4e18-9b42-22cca57e2372
last(data.raw_data, 10)

# ╔═╡ fd43e78e-38fa-4856-88e6-2ad8d8283cb1
random_numbers = randperm(39)

# ╔═╡ c4a871c2-f5c9-417a-9b9a-861957e290a0
begin
    function generate_g3_13()
        random_numbers = randperm(39)
        return sort.([random_numbers[i:i+2] for i in 1:3:length(randperm(39))])
    end

    function generate_g6_7()
        random_numbers = randperm(39)
        g42 = vcat(random_numbers, random_numbers[[1, 7, 13]])
        [g42[i:i+5] for i in 1:6:42]
    end

	function generate_g7_6()
        random_numbers = randperm(39)
        g42 = vcat(random_numbers, random_numbers[[1, 6, 12]])
        [g42[i:i+6] for i in 1:7:42]
    end
	
	function generate_g12_4()
        random_numbers = randperm(39)
        g48 = vcat(random_numbers, random_numbers[1:9])
        [g48[i:i+11] for i in 1:12:48]
    end
	
    # Precompute random combinations once
	const part7s = vcat([generate_g6_7() for _ in 1:100]...)
    # const part6s = vcat([generate_g7_6() for _ in 1:100]...)
	# serialize("data/part6s.jls", part6s)
	# const part6s = deserialize("data/part6s.jls")
    # const g1s = collect(combinations(1:39, 1))
	# serialize("data/g1s.jls", g1s)
	# const g1s = deserialize("data/g1s.jls")
	# const g2s = shuffle!(collect(combinations(1:39, 2)))
	# serialize("data/g2s.jls", g2s)
	const g2s = deserialize("data/g2s.jls")
	# const g3s = collect(combinations(1:39, 3))
	# serialize("data/g3s.jls", g3s)
    # const g3s = deserialize("data/g3s.jls")
	# const part13s = unique(vcat([generate_g3_13() for _ in 1:100]...))
	# serialize("data/part13s.jls", part13s)
	const part6s = unique(vcat([generate_g7_6() for _ in 1:100]...))
	# serialize("data/part6s.jls", part6s)
    const part13s = deserialize("data/part13s.jls")
	# const part13sidx = [generate_g3_13() for _ in 1:100]
	# serialize("data/part13sidx.jls", part13sidx)
	const part13sidx = deserialize("data/part13sidx.jls")
	# const g5s_samecount39 = deserialize("data/g5s_samecount39.jls")
	const part4s = unique(vcat([generate_g12_4() for _ in 1:100]...))
end

# ╔═╡ 399e6058-ce84-4f62-8d8c-5a63447dc531
generate_g12_4()

# ╔═╡ 4ade4b71-d338-42c0-bc2b-5e4a5a3559d8
pwd()

# ╔═╡ 4c3192b1-92f7-4b44-bc37-7d995898db38
part13s

# ╔═╡ 70e2c383-47da-4e87-8df5-8155a06ffec9
md"""### 自選號碼"""

# ╔═╡ 57c068ef-9140-4a6f-9cbc-1b5db5a0da0b
2/7

# ╔═╡ 6a87a303-ea11-4ade-8c62-0610ba308133
md"""
## selected\_rows multi\_pattern\_win\_info
"""

# ╔═╡ 8f99183f-a942-49dd-a777-bb753ed0ef86
# begin
# 	if !isnothing(get(found_gxs_in,gx,nothing))
# 		println(first(get(found_gxs_in,gx,nothing)))
# 		println(last(get(found_gxs_in,gx,nothing)))
# 	end
# end

# ╔═╡ 44b7d81a-3af9-418e-bcc4-ec1b293ce1ff
# begin
# 		println("下期中獎大於等於2的組合紀錄:")
# 		下期中獎大於等於2的組合紀錄 = deserialize("output/下期中獎大於等於2的組合紀錄_純粹查找/pure_ge2wins_fan5_pure_ge2wins_20250504_111755.jls")
# 		# 下期中獎大於等於2的組合紀錄 = collect(下期中獎大於等於2的組合紀錄)
# end

# ╔═╡ 3926079b-c192-4d8b-9979-23596d4218d6
# 下期中獎大於等於2的組合紀錄[1][1]

# ╔═╡ 423ad10d-62ab-41e1-8cf5-98e64e4be3b3
# 下期中獎大於等於2的組合紀錄list = first.(filter(x->last(x) == vlineat7rows, 下期中獎大於等於2的組合紀錄))

# ╔═╡ 607de4af-7c69-465b-98c3-98afced5dd3c


# ╔═╡ 06d5b96f-a539-4e2d-b704-5dc9eeb6cef1
# begin

	
# 	found_gxs_in2_0 = deserialize("e:/obsidian/julia/juliaproject/pricepatterns/output/matching_gxs/matching_gxs_fan5_s1050_20250406_090901.jls")
# 	found_gxs_in2 = collect.(first.(found_gxs_in2_0))
# 	# found_gxs_in2 = [[22,35]]
# end

# ╔═╡ 0ee33fef-7c8d-4d19-b4f4-56a40533fba6
## 顯示found_gxs_in2機率訊息
# filter(x-> first(x) == gx,(found_gxs_in2_0))[1][3]

# ╔═╡ 8302985e-8574-407b-a7c0-6cfeeb36a49b
自選號碼tw539_0501 = [[5, 30, 32],[8, 28, 38],[11, 29, 38],[2, 21, 35],
[12, 22, 37],[2, 28, 38],[16, 34, 38],[16, 18, 30],[21, 22, 39]]

# ╔═╡ 6bdafcd6-f48b-40aa-bac6-9c6dfef6ab58
自選號碼tw539 = [[05,17,39],[28,36,39],[01,10,26],
[01,03,23],[22,37,39],[19,26,27],[18,33,34],[05,15,25],
	
	[7, 9, 25],[8, 18, 29],[28, 32, 39],[5, 29, 31],[4, 5, 8],
]

# ╔═╡ 32745471-ef11-4f79-9539-fbc74b3a4c72
暫選號碼=[[25,27,28]]


# ╔═╡ 94a1c7e3-99c9-4ea6-a987-7d97a8de1501
投資fan5 = Dict(
	"2025-05-07"=>([3, 18, 27],[16, 19, 30],[10, 35, 37]),
	"2025-05-08"=>([9, 20, 36],[5, 13, 34],[4, 7, 10]),
	"2025-05-09"=>([8, 20, 34],[2, 4, 35],[3, 14, 16]),
	"2025-05-10"=>([1, 22, 34],[2, 26, 28],[10, 25, 36],)
)

# ╔═╡ 2cfcff2d-0e93-482d-9c6b-2b6b853f0a3d
投資fan5["2025-05-08"]

# ╔═╡ 3a063e60-6a6e-45d6-b259-a06590e03bc4
投資tw539 = Dict(
	"2025-05-07"=>([13, 33, 39],[16, 18, 30],[11, 29, 38]),
	"2025-05-08"=>([3, 30, 38],[8, 28, 34],[21, 23, 36]),
	"2025-05-09"=>([2, 10, 20],[7, 14, 34],[18, 33, 36],),
	"2025-05-10"=>([13, 26, 34],[9, 16, 21],[7, 17, 20],)
)

# ╔═╡ eca816c2-1003-4a51-af73-5dac1966e783
# 2025-05-18,01,07,33,36,38	
[[5, 14, 17],[2, 6, 39],[6, 18, 35],[14, 31, 36],[6, 24, 30],[6, 35, 38],
[6, 36, 38],[6, 25, 35],[22, 25, 36],[22, 24, 38],[18, 22, 35],[6, 17, 38],[5, 22, 24],[2, 6, 31],[2, 14, 15],[18, 22, 24],[2, 6, 15],[15, 22, 24],[6, 30, 36],[6, 31, 35],[2, 5, 14],[6, 28, 36],[14, 23, 36],[2, 6, 23],[6, 9, 35],
]

# ╔═╡ e474d169-21ac-4e84-b502-160428120014
function trans_gx_to_str(gx)
	join(string.(gx, pad=2), ",")
end

# ╔═╡ 3e8d13ab-f3c7-4137-9921-87cf0cdbdb34
# begin
# # 		println("載入符合定義條件的號碼組合:")
found_gxs_in1 = deserialize("results/tw539_gxs_p1171_hr_min_0.16_max_nothing_inst_min_2_max_nothing.jls")
# found_gxs_in1 = deserialize("fan5_all_matched_gxs_at_period_1147_hit_rate_0.166_num_instances_40_gx_wins_lookback_5_pattern_lookback_5.jls")
# "fan5_all_matched_gxs_at_period_1132_hit_rate_0.163_num_instances_4.jls"
# fan5_all_matched_gxs_at_period_1142_hit_rate_0.2_num_instances_5_gx_wins_lookback_5_pattern_lookback_5

# 	# found_gxs_in1 = deserialize("output/找到符合定義條件的號碼組合/filtered_gxs_tw539_s1136_custom_conditions3_w_0002.jls")
	
# 		found_gxs_in1 = collect(found_gxs_in1)
# end

# ╔═╡ 24c4cb9f-fad3-4906-8d4e-870ac1248ccf
begin
# 	# begin
# 	# 		# Predefined number groups (const for performance)
# 	# 		const 自選號碼fan = [[5, 26, 38],[21, 26, 38],]
		
# 	# 	    # Add other predefined groups similarly...
		    const 自選號碼1 = found_gxs_in1 #specific_pattern_stats.matching_combinations
# 			const 自選號碼2 = found_gxs_in2
		# 下期中獎大於等於2的組合紀錄
			# const 自選號碼3 = 下期中獎大於等於2的組合紀錄
# 	# end
end

# ╔═╡ d64aa272-60b0-4bf1-bf33-dc7348c02581
# found_gxs_in1_nextday_wins_numbers = intersect.(found_gxs_in1,[data_s[1132]])

# ╔═╡ 273aa79f-cae0-42fa-82bf-bfbae74d436b
# count2=sum(binomial.(length.(found_gxs_in1_nextday_wins_numbers),2))

# ╔═╡ ba5bd2f6-3139-49c0-939a-340e0a8a7a28
# return_money2=sum(binomial.(length.(found_gxs_in1_nextday_wins_numbers),2)) * 53

# ╔═╡ d5699b4f-ca9d-4bba-8173-bbdf5ed1ca0c
# cost2=length(found_gxs_in1_nextday_wins_numbers)*3*0.8

# ╔═╡ 6d3c1881-81ab-4d37-82fc-3a2375919cc3
# profit2=return_money2-cost2

# ╔═╡ 90bf8f8c-046f-4999-a9ac-8b6ce2c41cd8
# (return_money2-cost2)/cost2

# ╔═╡ 830eee87-a422-436c-b8c9-8f1677974817
# return_money = sum(length.(found_gxs_in1_nextday_wins_numbers))*212

# ╔═╡ ac277bf7-a042-4ab4-ba8a-d55e3b038dab
# cost = length(found_gxs_in1_nextday_wins_numbers) * 91.2

# ╔═╡ cd13189e-3626-468d-b484-4b01b6266ecb
# return_money/cost


# ╔═╡ aace243e-bd5f-4aaa-b5f2-4e6d4002a163
# (return_money-cost)/cost

# ╔═╡ de3182c9-1d7c-4937-8f2c-07bce0070ec9
自選號碼fan=[[07,32,36],[07,18,24],[04,09,12],[28,36,39],
]

# ╔═╡ d74023f8-4098-4371-9055-39bc047d0478
filter(x-> 19 in x,自選號碼fan)

# ╔═╡ 340f88d7-2abd-4dd3-a782-30a80c6414e9
watchlist_tw539 = [[19,26],
[01,10,26],[05,29,37],[09,24,35],[02,28,32],
# ---	
	[05,17,39],[28,36,39],[19,26,27],
[01,03,23],[22,37,39],[18,33,34],[05,15,25],
[16,18,35],[10,25,36],[01,06,26],[01,23,24],[10,35,39],[18,24,28],[05,11,26],[12,23,28],[01,06,24],[03,05,17],[05,10,39],[02,10,31],[05,25,37],[05,22,37],[06,13,15],[01,22,24],[05,18,26],[12,23,37],[05,06,25],[01,24,37],[05,09,27],[06,12,19],[05,18,37],[06,10,29],[05,16,37],
# 645
]

# ╔═╡ 66ffc948-8040-44e4-8d97-57a31dd44e70
watchlist_fan5 = [Int8[35, 36],
Int8[4, 35],

Int8[18, 37],
Int8[36, 37],
Int8[4, 37],
Int8[34, 37],	
#1160
[5, 25],[6, 29],[6, 34],[6, 18],[1, 6],[6, 33],[6, 11],[6, 13],[13, 29],[29, 37],[5, 7],[5, 10],[7, 25],[5, 20],[10, 25],[20, 25],[14, 27],[27, 38],[22, 39],[5, 13],[13, 25],[5, 29],[25, 29],[29, 39],
#1161
[22, 36],[4, 22],[32, 34],[27, 34],[32, 37],[1, 34],[14, 37],[37, 38],[4, 18],[18, 36],[8, 37],[8, 32],[8, 34],[22, 28],[22, 23],[6, 22],[27, 37],[18, 37],[8, 22],[14, 22],[22, 38],[5, 7],[5, 10],[7, 25],[5, 20],[10, 25],[20, 25],[18, 27],[22, 35],
]

# ╔═╡ b0248751-5f3c-418d-8d41-10ae41fdccb4
# 或者如果你偏好使用核取方塊：
md"""
#### 保存圖表
$(@bind save_checkbox CheckBox()) 保存圖表
"""

# ╔═╡ 46eec85f-9e59-47de-831e-f6944d4ca377
begin
    md"""
chartdays: $(@bind chartdays NumberField(5:5:365, default=50))
gx7ndays: $(@bind gx7ndays NumberField(1:100, default=7))
gx7nthindex: $(@bind gx7nthindex NumberField(1:size(nx,1), default=size(nx,1)))
gx7index: $(@bind gx7index NumberField(1:1:9139, default=1))
dayback7: $(@bind dayback7 NumberField(0:1000, default=0))
vlineat7: $(@bind vlineat7 NumberField(1:365, default=50))
vlineat72: $(@bind vlineat72 NumberField(1:365, default=1))
patterns\_index: $(@bind patterns_index NumberField(1:1000, default=1))
chartsize\_x: $(@bind chartsize_x NumberField(100:10:680,default=600))
chartsize\_y: $(@bind chartsize_y NumberField(100:10:680,default=680))
"""
	
end

# ╔═╡ c55746f6-1443-4ab9-86db-457c2f1536ca
vlineat7rows = data_rows - (chartdays - vlineat7) - dayback7

# ╔═╡ 88a69b5d-60df-45ab-9f6a-2e2ee7eac989
let  # 中球數,出現次數,中球次數,中球次數比率(0.4285),中球數比率(0.14285)
	patterns_window = 4
    selected_rows = data_rows - (chartdays - vlineat7) - dayback7
	if length(cache[].gx_wins) != 0 
		selected_rows_multi_pattern = [
			cache[].gx_wins[selected_rows-patterns_window+1:selected_rows],
			cache[].cum7[selected_rows-patterns_window+1:selected_rows],
			cache[].cum14[selected_rows-patterns_window+1:selected_rows],
			cache[].cum21[selected_rows-patterns_window+1:selected_rows]
		]
		# pattern_infos = get(multi_pattern_win_info,selected_rows_multi_pattern,"找不到此Pattern");
		# println("$(pattern_infos[1]),$(pattern_infos[2]),$(round(pattern_infos[3],digits=3)),$(round(pattern_infos[4],digits=3))")
	end
end

# ╔═╡ 01425fc1-fc28-40c4-872c-ffcef57764f6
# 圖表標籤
tags = ["",
    "坐車價格V型反轉",
    "坐車價格底部盤整",
    "坐車價格V型反轉點正位於前一個跌不下去的點",
    "坐車價格由跌多漲少轉漲多跌少",
    "藍線止跌",
    "藍線15日內產生劇烈的波峰與波谷波距6點",
    "紅線14日反轉大跌後持平",
    "紅線形成7日V底",
    "紅V底",
    "藍V底",
    "紅U底",
    "藍U底",
    "紅線止跌",
    "藍紅由下往上共點0",
    "藍由下往上共點0",
    "紅由下往上共點0",
    "藍紅共點2",
    "藍紅共點-1",
    "藍紅共點-2",
    "藍紅兩線同時由下往上",
    "藍紅正開始止跌聚集交叉轉換或V形反轉",
    "有一線正與0共點",
    "兩點共點",
    "兩點共點0",
    "兩線正在交叉"]

# ╔═╡ 28151031-5782-4582-98c7-afe7f24d41d4
md"""
#### 圖表標籤選擇
$(@bind chart_tags MultiSelect(tags, default=String[]))
"""

# ╔═╡ 6eb799dd-0bf1-4cb0-b5fe-d6ddcad0ee8f
println(part13sidx[patterns_index])

# ╔═╡ 4bcd7bd6-f4f1-46d5-8ecf-435941bcbcd4
begin

    # ... (other predefined lists) ...

    # Function to select the group based on watchlist
    function select_group(watchlist::String, index::Int, patterns_index::Int)
        if watchlist == "自選號碼fan"
            return sort(自選號碼fan[index])
		elseif watchlist == "自選號碼tw539"
            return sort(自選號碼tw539[index])
		elseif watchlist == "watchlist_tw539"
            return sort(watchlist_tw539[index])
		elseif watchlist == "watchlist_fan5"
            return sort(watchlist_fan5[index])
        elseif watchlist == "自選號碼1"
            return 自選號碼1[index]
		elseif watchlist == "自選號碼2"
            return 自選號碼2[index]
		elseif watchlist == "暫選號碼"
            return 暫選號碼[index]
		elseif watchlist == "下期中獎大於等於2的組合紀錄list"
            return 下期中獎大於等於2的組合紀錄list[index]
		elseif watchlist == "g5s_samecount39"
            return g5s_samecount39[index]
        # ... (other watchlist options) ...
		elseif watchlist == "part7s"
            return sort(part7s[index])
        elseif watchlist == "part13s"
            return sort(part13s[index])
		elseif watchlist == "part4s"
            return sort(part4s[index])
		elseif watchlist == "part6s"
            return sort(part6s[index])
        elseif watchlist == "part13sidx"
            return sort(part13sidx[patterns_index][index])
        elseif watchlist == "g2s"
            return sort(g2s[index])
		elseif watchlist == "g1s"
            return sort(g1s[index])
        else
            error("Invalid watchlist")
        end
    end


    watchlist = "g2s"  #  Can be a @bind as well
    gx = select_group(watchlist, gx7index, patterns_index)  # Pass necessary indices
    println("$(watchlist), $(gx)")

    # NOW we can initialize/update the cache:
    _calculate_prices!(gx, cache) # Initial calculation, now that gx is defined.
	calculate_prices_star2!(gx, cache2) # Initial calculation, now that gx is defined.
    gx  # return gx so it can be used below
end

# ╔═╡ e3a4f8b5-be50-495d-801b-08bd0e2bd5cf
begin
	## 擷取中球數(gx_wins)區間 patterns
	let
		gx_wins_window = 12
		patterns_window = 3		
		selected_rows = data_rows - (chartdays - vlineat7) - dayback7

		varname  = "$(game)_$(vlineat7rows)_custom_conditions_g$(length(gx))_gw$(gx_wins_window)_r$(patterns_window)_00 = "
		varname2  = "$(game)_$(vlineat7rows)_custom_conditions_g$(length(gx))_gw$(gx_wins_window)_r$(patterns_window)_$(join(string.(gx,pad=2),"_")) = "
		
		prefix_1 = "(cum7, cum14, cum21, gx_wins, row) -> gx_wins[row-$(gx_wins_window)+1:row] == "
		prefix_2 = "(cum7, cum14, cum21, gx_wins, row) -> cum7[row-$(patterns_window)+1:row] == "
		prefix_3 = "(cum7, cum14, cum21, gx_wins, row) -> cum14[row-$(patterns_window)+1:row] == "
		prefix_4 = "(cum7, cum14, cum21, gx_wins, row) -> cum21[row-$(patterns_window)+1:row] == "
		
		if length(cache[].gx_wins) != 0 
	    	println(varname,"[\n",prefix_1,cache[].gx_wins[selected_rows-gx_wins_window+1:selected_rows],",")
			
			println(prefix_2,cache[].cum7[selected_rows-patterns_window+1:selected_rows],",")
			println(prefix_3,cache[].cum14[selected_rows-patterns_window+1:selected_rows],",")
			println(prefix_4,cache[].cum21[selected_rows-patterns_window+1:selected_rows],"]")
			println()
			pattern_info = [cache[].gx_wins[selected_rows-gx_wins_window+1:selected_rows],cache[].cum7[selected_rows-patterns_window+1:selected_rows],cache[].cum14[selected_rows-patterns_window+1:selected_rows],cache[].cum21[selected_rows-patterns_window+1:selected_rows]]
			println("pattern_info = [$(pattern_info),","[\"$(game)\",$(vlineat7rows),$(Int64.(gx))]]")
			println()
			println("[\"$(game)\",$(vlineat7rows),$(Int64.(gx))]")
			println()
			println(varname2,"[\n",prefix_1,"pattern_info[1]",",")
			println(prefix_2,"pattern_info[2]",",")
			println(prefix_3,"pattern_info[3]",",")
			println(prefix_4,"pattern_info[4]","]\n")

			
		end
		# Access cached data
	end
end

# ╔═╡ 46773db9-d951-45c5-9588-c7aafcbe1bae
# println("gx=$(gx);get_gx_pattern_probability(gx,$(vlineat7rows), pattern_win_info, gxsprices, patterns_window)")
println("[$(trans_gx_to_str(gx))],")


# ╔═╡ f4e79b84-a224-4040-b6d2-a93f231ba557
g3_13_saved = [
[[3, 11, 28], [16, 22, 33], [18, 19, 21], [5, 20, 36], [2, 29, 35], [24, 32, 38], [4, 14, 37], [1, 13, 15], [7, 9, 39], [23, 27, 30], [8, 10, 12], [17, 25, 31], [6, 26, 34]]
]

# ╔═╡ b84a1cc8-3cc2-4eac-910a-79f3f56edbd4
function save_chart(chart, chartdir; tags=String[])
    title = chart[1].attr[:title] # Get the title from the first subplot
    # Clean the title to make it a valid filename
    filename = replace(title, r"[<>:\"/\\|?*]" => "_") # Replace invalid filename characters

    # Create paths for both PNG and markdown files
    png_path = joinpath(chartdir, filename * ".png")
    md_path = joinpath(chartdir, filename * ".md")

    # Save the chart as PNG
    Plots.savefig(chart, png_path)

    # 生成標籤 frontmatter
    tags_str = isempty(tags) ? "" : join(["  - " * tag for tag in tags], "\n")
    current_date = Dates.format(Dates.now(), "yyyy-mm-dd")

    # Create markdown content with chart information
    md_content =
        """
---
tags:
$tags_str
aliases:
file_title: $(filename)
created_date: $current_date
---

#G$(length(gx))標準圖 #$(game)

## Chart Analysis: $title

![Chart](./$filename.png)

### Chart Details
- Title: $(chart[1].attr[:title])
- Generated: $(Dates.format(Dates.now(), "yyyy-mm-dd HH:MM:SS"))

### Notes
- Top panel shows price trends (black line)
- Bottom panel shows:
  - 7-day moving average (blue)
  - 14-day moving average (green)
  - 21-day moving average (red)
- Orange vertical line marks the reference point
"""

    # Write markdown file
    write(md_path, md_content)

    return png_path
end

# ╔═╡ b7f6fd98-5593-43bc-ac0e-b6ed5978a524
# This is now a simple calculation, no need for a function.
plotrang = data_rows - dayback7 - chartdays + 1:data_rows - dayback7

# ╔═╡ 98dd1055-2574-4d9d-8c40-0b7531c339c3
begin
    # Plotting cell (only reruns when inputs change)
    # Put EVERYTHING inside a `let` block. This creates a local scope
    # and avoids global variables, which are bad for reactivity.
    let
        function trans_gx_to_str(gx)
            join(string.(gx, pad=2), ",")
        end

        # Check if gx has changed, and update the cache if necessary.
        if gx != cache[].last_gx  # Correct comparison!
            calculate_prices!(gx, cache)
        end

        # Now, access data from the cache
        price7 = cache[].price
        cum7 = cache[].cum7
        cum14 = cache[].cum14
        cum21 = cache[].cum21
        gx_wins7 = cache[].gx_wins

		# pg7_miny = ceil(minimum(vcat(cum7[plotrang], cum14[plotrang], cum21[plotrang]))) - 1
  #       pg7_maxy = ceil(maximum(vcat(cum7[plotrang], cum14[plotrang], cum21[plotrang]))) + 1

		pg7_miny = -7 #-7
		pg7_maxy = 7 #7
		
        vlineat7rows = data_rows - (chartdays - vlineat7) - dayback7

		# begin
	  	# # 中球數,出現次數,中球次數,中球次數比率(0.4285),中球數比率(0.14285)
		# # patterns_window = 4
	    # selected_rows = data_rows - (chartdays - vlineat7) - dayback7
		# if length(cache[].gx_wins) != 0 
			# selected_rows_multi_pattern = [
				# cache[].gx_wins[selected_rows-patterns_window+1:selected_rows],
				# cache[].cum7[selected_rows-patterns_window+1:selected_rows],
				# cache[].cum14[selected_rows-patterns_window+1:selected_rows],
				# cache[].cum21[selected_rows-patterns_window+1:selected_rows]
			# ]
			# pattern_infos = get(multi_pattern_win_info,selected_rows_multi_pattern,(00,00,00,00,00));# println(pattern_infos)
			# pattern_infos_str = "$(pattern_infos[1]), $(pattern_infos[2]), $(pattern_infos[3]), $(round(pattern_infos[4],digits=3)), $(round(pattern_infos[5],digits=3))"
		# end
		# end
		# price_miny = minimum(price7[plotrang])
		# price_maxy = price_miny + 1.4
        p1 = plot(
            price7[plotrang],
            linecolor=:black,
            mark=:o,
            markersize=0.7,
            leg=false,
            titlefontsize=8,
			title="$(game)_$(trans_gx_to_str(gx))_@$(vlineat7rows)_($(data.dates[vlineat7rows]))_@$(data_rows-dayback7)",
			# ylimits=(price_miny, price_maxy)
        )
        vline!([vlineat7], c="orange")
        vline!([vlineat72], c="grey")

        p2 = plot(
            [cum7[plotrang], cum14[plotrang], cum21[plotrang]],
            linecolor=["blue" "green" "red"],
            linewidth=1.0,
            mark=:o,
            markersize=0.7,
            leg=false ,
            yticks=pg7_miny:1:pg7_maxy+1,
            ylimits=(pg7_miny, pg7_maxy)
        )
        hline!([0], linestyle=:dash, color=:grey, label="Balance")
        vline!([vlineat7], c="orange")
        vline!([vlineat72], c="grey")

        pg7 = plot(p1, p2, layout=(2, 1), size=(chartsize_x, chartsize_y))

        # 響應核取方塊的變化 (still inside the `let` block)
        if save_checkbox
			timestamp = Dates.format(now(), "yyyymmdd")
            chartspath = mkpath("./charts/$(game)/$(timestamp)_g$(length(gx))")
            saved_path = save_chart(pg7, chartspath, tags=chart_tags)
        end
        pg7  # Return the plot
    end
end

# ╔═╡ 1033d92b-c35b-4231-b8d6-17ba89b10ad5
begin
    # Plotting cell (only reruns when inputs change)
    # Put EVERYTHING inside a `let` block. This creates a local scope
    # and avoids global variables, which are bad for reactivity.
    let
        function trans_gx_to_str(gx)
            join(string.(gx, pad=2), ",")
        end

        # Check if gx has changed, and update the cache if necessary.
        if gx != cache2[].last_gx  # Correct comparison!
            calculate_prices_star2!(gx, cache2)
        end

        # Now, access data from the cache
        price7 = cache2[].price
        cum7 = cache[].cum7
        cum14 = cache[].cum14
        cum21 = cache[].cum21
        gx_wins7 = cache[].gx_wins

		# pg7_miny = ceil(minimum(vcat(cum7[plotrang], cum14[plotrang], cum21[plotrang]))) - 1
  #       pg7_maxy = ceil(maximum(vcat(cum7[plotrang], cum14[plotrang], cum21[plotrang]))) + 1

		pg7_miny = -5 #-7
		pg7_maxy = 5 #7
		
        vlineat7rows = data_rows - (chartdays - vlineat7) - dayback7

		# price_miny = minimum(price7[plotrang])
		# price_maxy = price_miny + 1.4
        p1 = plot(
            price7[plotrang],
            linecolor=:black,
            mark=:o,
            markersize=0.7,
            leg=false,
            titlefontsize=8,
			title="$(game)_$(trans_gx_to_str(gx))_@$(vlineat7rows)_($(data.dates[vlineat7rows]))_@$(data_rows-dayback7)",
			# ylimits=(price_miny, price_maxy)
        )
        vline!([vlineat7], c="orange")
        vline!([vlineat72], c="grey")

        p2 = plot(
            [cum7[plotrang], cum14[plotrang], cum21[plotrang]],
            linecolor=["blue" "green" "red"],
            linewidth=1.0,
            mark=:o,
            markersize=0.7,
            leg=false #,
            #yticks=pg7_miny:1:pg7_maxy+1,
            #ylimits=(pg7_miny, pg7_maxy)
        )
        hline!([0], linestyle=:dash, color=:grey, label="Balance")
        vline!([vlineat7], c="orange")
        vline!([vlineat72], c="grey")

        pg7 = plot(p1, p2, layout=(2, 1), size=(chartsize_x, chartsize_y))

        # 響應核取方塊的變化 (still inside the `let` block)
        if save_checkbox
			timestamp = Dates.format(now(), "yyyymmdd")
            chartspath = mkpath("./charts/$(game)/$(timestamp)_g$(length(gx))_star2")
            saved_path = save_chart(pg7, chartspath, tags=chart_tags)
        end
        pg7  # Return the plot
    end
end

# ╔═╡ 1ee4d26b-042c-411b-8d25-0ea22dbac330
# ╠═╡ disabled = true
#=╠═╡
let
	# --- 在 Pluto Cell 中 ---
	# using DynamicAxisWarping # 假設使用這個包
	# using Distances: SqEuclidean # <-- Add this line
	using Distances
	# 1. 假設用戶已通過 @bind 選擇了 target_gx 和 num_similar
	target_gx = [1, 2, 3] # 範例
	num_similar = 10      # 範例
	
	# 2. 計算目標序列 (假設 cache, plotrang 已定義)
	calculate_prices!(target_gx, cache)
	target_price_segment = cache[].price[plotrang]
	# 你也可以提取 cum7, cum14, cum21 的片段
	
	# 3. 選擇候選 gxs (例如，使用 part13s)
	candidate_gxs = part13s # 或者 combinations(1:39, length(target_gx))
	
	# 4. 計算相似度
	similarity_results = []
	temp_cache = Ref{CalculationCache}(CalculationCache([], [], [], [], [], [])) # 臨時 cache 避免干擾主圖
	
	println("Calculating similarities for $(length(candidate_gxs)) candidates...")
	for candidate_gx in candidate_gxs
	    if candidate_gx == target_gx
	        continue # 跳過自己
	    end
	    try
	        # 計算候選序列
	        calculate_prices!(candidate_gx, temp_cache)
	        candidate_price_segment = temp_cache[].price[plotrang]
	
	        # 計算 DTW 距離 (或其他指標)
	        # 注意：確保序列長度相同且不為空
	        if length(target_price_segment) == length(candidate_price_segment) && !isempty(target_price_segment)
	            dist = dtw_cost(target_price_segment, candidate_price_segment, SqEuclidean(),1)# 使用歐氏距離作為基底
	            push!(similarity_results, (candidate_gx, dist))
	        else
	             println("Warning: Skipping candidate $candidate_gx due to length mismatch or empty segment.")
	        end
	    catch e
	        println("Error processing candidate $candidate_gx: $e")
	        # 可能某些 gx 計算會出錯，例如數據不足等
	    end
	end
	println("Calculation complete.")
	
	# 5. 排序並選出最相似的
	sort!(similarity_results, by = x -> x[2]) # 按距離升序排序
	
	# 6. 顯示結果
	top_similar_gxs = first(similarity_results, num_similar)
	
	println("\nTop $num_similar gxs similar to $target_gx (based on price DTW distance):")
	for (gx, dist) in top_similar_gxs
	    println("GX: $gx, Distance: $(round(dist, digits=4))")
	end
	
	# 你可以將 top_similar_gxs 傳遞給另一個 Cell 或修改現有 Cell 的 watchlist
	# 以便快速查看這些相似 gx 的圖表。
	
	
	
end
  ╠═╡ =#

# ╔═╡ 00000000-0000-0000-0000-000000000001
PLUTO_PROJECT_TOML_CONTENTS = """
[deps]
CSV = "336ed68f-0bac-5ca0-87d4-7b16caf5d00b"
Combinatorics = "861a8166-3701-5b0c-9a16-15d98fcdc6aa"
DataFrames = "a93c6f00-e57d-5684-b7b6-d8193f3e46c0"
Dates = "ade2ca70-3891-5945-98fb-dc099432e06a"
DynamicAxisWarping = "aaaaaaaa-4a10-5553-b683-e707b00e83ce"
OrderedCollections = "bac558e1-5e72-5ebc-8fee-abe8a469f55d"
Plots = "91a5bcdd-55d7-5caf-9e0b-520d859cae80"
PlutoUI = "7f904dfe-b85e-4ff6-b463-dae2292396a8"
Random = "9a3f8284-a2c9-5f02-9a11-845980a1fd5c"
Serialization = "9e88b42a-f829-5b0c-bbe9-9e923198166b"
StaticArrays = "90137ffa-7385-5640-81b9-e52037218182"
Statistics = "10745b16-79ce-11e8-11f9-7d13ad32a3b2"
StatsBase = "2913bbd2-ae8a-5f71-8c99-4fb6c76f3a91"
ThreadSafeDicts = "4239201d-c60e-5e0a-9702-85d713665ba7"

[compat]
CSV = "~0.10.15"
Combinatorics = "~1.0.2"
DataFrames = "~1.7.0"
DynamicAxisWarping = "~0.4.18"
OrderedCollections = "~1.8.0"
Plots = "~1.40.9"
PlutoUI = "~0.7.61"
StaticArrays = "~1.9.13"
Statistics = "~1.11.1"
StatsBase = "~0.34.4"
ThreadSafeDicts = "~0.1.6"
"""

# ╔═╡ 00000000-0000-0000-0000-000000000002
PLUTO_MANIFEST_TOML_CONTENTS = """
# This file is machine-generated - editing it directly is not advised

julia_version = "1.11.2"
manifest_format = "2.0"
project_hash = "4b7a55d587e25268117f89b4c70e67e1a362cd43"

[[deps.AbstractFFTs]]
deps = ["LinearAlgebra"]
git-tree-sha1 = "d92ad398961a3ed262d8bf04a1a2b8340f915fef"
uuid = "621f4979-c628-5d54-868e-fcf4e3e8185c"
version = "1.5.0"

    [deps.AbstractFFTs.extensions]
    AbstractFFTsChainRulesCoreExt = "ChainRulesCore"
    AbstractFFTsTestExt = "Test"

    [deps.AbstractFFTs.weakdeps]
    ChainRulesCore = "d360d2e6-b24c-11e9-a2a3-2a2ae2dbcce4"
    Test = "8dfed614-e22c-5e08-85e1-65c5234f0b40"

[[deps.AbstractPlutoDingetjes]]
deps = ["Pkg"]
git-tree-sha1 = "6e1d2a35f2f90a4bc7c2ed98079b2ba09c35b83a"
uuid = "6e696c72-**************-42206c756150"
version = "1.3.2"

[[deps.Adapt]]
deps = ["LinearAlgebra", "Requires"]
git-tree-sha1 = "f7817e2e585aa6d924fd714df1e2a84be7896c60"
uuid = "79e6a3ab-5dfb-504d-930d-738a2a938a0e"
version = "4.3.0"
weakdeps = ["SparseArrays", "StaticArrays"]

    [deps.Adapt.extensions]
    AdaptSparseArraysExt = "SparseArrays"
    AdaptStaticArraysExt = "StaticArrays"

[[deps.AliasTables]]
deps = ["PtrArrays", "Random"]
git-tree-sha1 = "9876e1e164b144ca45e9e3198d0b689cadfed9ff"
uuid = "66dad0bd-aa9a-41b7-9441-69ab47430ed8"
version = "1.1.3"

[[deps.ArgTools]]
uuid = "0dad84c5-d112-42e6-8d28-ef12dabb789f"
version = "1.1.2"

[[deps.ArrayInterface]]
deps = ["Adapt", "LinearAlgebra"]
git-tree-sha1 = "017fcb757f8e921fb44ee063a7aafe5f89b86dd1"
uuid = "4fba245c-0d91-5ea0-9b3e-6abc04ee57a9"
version = "7.18.0"

    [deps.ArrayInterface.extensions]
    ArrayInterfaceBandedMatricesExt = "BandedMatrices"
    ArrayInterfaceBlockBandedMatricesExt = "BlockBandedMatrices"
    ArrayInterfaceCUDAExt = "CUDA"
    ArrayInterfaceCUDSSExt = "CUDSS"
    ArrayInterfaceChainRulesCoreExt = "ChainRulesCore"
    ArrayInterfaceChainRulesExt = "ChainRules"
    ArrayInterfaceGPUArraysCoreExt = "GPUArraysCore"
    ArrayInterfaceReverseDiffExt = "ReverseDiff"
    ArrayInterfaceSparseArraysExt = "SparseArrays"
    ArrayInterfaceStaticArraysCoreExt = "StaticArraysCore"
    ArrayInterfaceTrackerExt = "Tracker"

    [deps.ArrayInterface.weakdeps]
    BandedMatrices = "aae01518-5342-5314-be14-df237901396f"
    BlockBandedMatrices = "ffab5731-97b5-5995-9138-79e8c1846df0"
    CUDA = "052768ef-5323-5732-b1bb-66c8b64840ba"
    CUDSS = "45b445bb-4962-46a0-9369-b4df9d0f772e"
    ChainRules = "082447d4-558c-5d27-93f4-14fc19e9eca2"
    ChainRulesCore = "d360d2e6-b24c-11e9-a2a3-2a2ae2dbcce4"
    GPUArraysCore = "46192b85-c4d5-4398-a991-12ede77f4527"
    ReverseDiff = "37e2e3b7-166d-5795-8a7a-e32c996b4267"
    SparseArrays = "2f01184e-e22b-5df5-ae63-d93ebab69eaf"
    StaticArraysCore = "1e83bf80-4336-4d27-bf5d-d5a4f845583c"
    Tracker = "9f7883ad-71c0-57eb-9f7f-b5c9e6d3789c"

[[deps.Artifacts]]
uuid = "56f22d72-fd6d-98f1-02f0-08ddc0907c33"
version = "1.11.0"

[[deps.Base64]]
uuid = "2a0f44e3-6c83-55bd-87e4-b1978d98bd5f"
version = "1.11.0"

[[deps.Bessels]]
git-tree-sha1 = "4435559dc39793d53a9e3d278e185e920b4619ef"
uuid = "0e736298-9ec6-45e8-9647-e4fc86a2fe38"
version = "0.2.8"

[[deps.BinDeps]]
deps = ["Libdl", "Pkg", "SHA", "URIParser", "Unicode"]
git-tree-sha1 = "1289b57e8cf019aede076edab0587eb9644175bd"
uuid = "9e28174c-4ba2-5203-b857-d8d62c4213ee"
version = "1.0.2"

[[deps.BitFlags]]
git-tree-sha1 = "0691e34b3bb8be9307330f88d1a3c3f25466c24d"
uuid = "d1d4a3ce-64b1-5f1a-9ba4-7e7e69966f35"
version = "0.1.9"

[[deps.BitTwiddlingConvenienceFunctions]]
deps = ["Static"]
git-tree-sha1 = "f21cfd4950cb9f0587d5067e69405ad2acd27b87"
uuid = "62783981-4cbd-42fc-bca8-16325de8dc4b"
version = "0.1.6"

[[deps.Bzip2_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "1b96ea4a01afe0ea4090c5c8039690672dd13f2e"
uuid = "6e34b625-4abd-537c-b88f-471c36dfa7a0"
version = "1.0.9+0"

[[deps.CPUSummary]]
deps = ["CpuId", "IfElse", "PrecompileTools", "Static"]
git-tree-sha1 = "5a97e67919535d6841172016c9530fd69494e5ec"
uuid = "2a0fbf3d-bb9c-48f3-b0a9-814d99fd7ab9"
version = "0.2.6"

[[deps.CSV]]
deps = ["CodecZlib", "Dates", "FilePathsBase", "InlineStrings", "Mmap", "Parsers", "PooledArrays", "PrecompileTools", "SentinelArrays", "Tables", "Unicode", "WeakRefStrings", "WorkerUtilities"]
git-tree-sha1 = "deddd8725e5e1cc49ee205a1964256043720a6c3"
uuid = "336ed68f-0bac-5ca0-87d4-7b16caf5d00b"
version = "0.10.15"

[[deps.Cairo_jll]]
deps = ["Artifacts", "Bzip2_jll", "CompilerSupportLibraries_jll", "Fontconfig_jll", "FreeType2_jll", "Glib_jll", "JLLWrappers", "LZO_jll", "Libdl", "Pixman_jll", "Xorg_libXext_jll", "Xorg_libXrender_jll", "Zlib_jll", "libpng_jll"]
git-tree-sha1 = "009060c9a6168704143100f36ab08f06c2af4642"
uuid = "83423d85-b0ee-5818-9007-b63ccbeb887a"
version = "1.18.2+1"

[[deps.CloseOpenIntervals]]
deps = ["Static", "StaticArrayInterface"]
git-tree-sha1 = "05ba0d07cd4fd8b7a39541e31a7b0254704ea581"
uuid = "fb6a15b2-703c-40df-9091-08a04967cfa9"
version = "0.1.13"

[[deps.CodecZlib]]
deps = ["TranscodingStreams", "Zlib_jll"]
git-tree-sha1 = "962834c22b66e32aa10f7611c08c8ca4e20749a9"
uuid = "944b1d66-785c-5afd-91f1-9de20f533193"
version = "0.7.8"

[[deps.ColorSchemes]]
deps = ["ColorTypes", "ColorVectorSpace", "Colors", "FixedPointNumbers", "PrecompileTools", "Random"]
git-tree-sha1 = "403f2d8e209681fcbd9468a8514efff3ea08452e"
uuid = "35d6a980-a343-548e-a6ea-1d62b119f2f4"
version = "3.29.0"

[[deps.ColorTypes]]
deps = ["FixedPointNumbers", "Random"]
git-tree-sha1 = "b10d0b65641d57b8b4d5e234446582de5047050d"
uuid = "3da002f7-5984-5a60-b8a6-cbb66c0b333f"
version = "0.11.5"

[[deps.ColorVectorSpace]]
deps = ["ColorTypes", "FixedPointNumbers", "LinearAlgebra", "Requires", "Statistics", "TensorCore"]
git-tree-sha1 = "a1f44953f2382ebb937d60dafbe2deea4bd23249"
uuid = "c3611d14-8923-5661-9e6a-0046d554d3a4"
version = "0.10.0"
weakdeps = ["SpecialFunctions"]

    [deps.ColorVectorSpace.extensions]
    SpecialFunctionsExt = "SpecialFunctions"

[[deps.Colors]]
deps = ["ColorTypes", "FixedPointNumbers", "Reexport"]
git-tree-sha1 = "64e15186f0aa277e174aa81798f7eb8598e0157e"
uuid = "5ae59095-9a9b-59fe-a467-6f913c188581"
version = "0.13.0"

[[deps.Combinatorics]]
git-tree-sha1 = "08c8b6831dc00bfea825826be0bc8336fc369860"
uuid = "861a8166-3701-5b0c-9a16-15d98fcdc6aa"
version = "1.0.2"

[[deps.CommonWorldInvalidations]]
git-tree-sha1 = "ae52d1c52048455e85a387fbee9be553ec2b68d0"
uuid = "f70d9fcc-98c5-4d4a-abd7-e4cdeebd8ca8"
version = "1.0.0"

[[deps.Compat]]
deps = ["TOML", "UUIDs"]
git-tree-sha1 = "8ae8d32e09f0dcf42a36b90d4e17f5dd2e4c4215"
uuid = "34da2185-b29b-5c13-b0c7-acf172513d20"
version = "4.16.0"
weakdeps = ["Dates", "LinearAlgebra"]

    [deps.Compat.extensions]
    CompatLinearAlgebraExt = "LinearAlgebra"

[[deps.CompilerSupportLibraries_jll]]
deps = ["Artifacts", "Libdl"]
uuid = "e66e0078-7015-5450-92f7-15fbd957f2ae"
version = "1.1.1+0"

[[deps.ConcurrentUtilities]]
deps = ["Serialization", "Sockets"]
git-tree-sha1 = "d9d26935a0bcffc87d2613ce14c527c99fc543fd"
uuid = "f0e56b4a-5159-44fe-b623-3e5288b988bb"
version = "2.5.0"

[[deps.ConstructionBase]]
git-tree-sha1 = "76219f1ed5771adbb096743bff43fb5fdd4c1157"
uuid = "187b0558-2788-49d3-abe0-74a17ed4e7c9"
version = "1.5.8"

    [deps.ConstructionBase.extensions]
    ConstructionBaseIntervalSetsExt = "IntervalSets"
    ConstructionBaseLinearAlgebraExt = "LinearAlgebra"
    ConstructionBaseStaticArraysExt = "StaticArrays"

    [deps.ConstructionBase.weakdeps]
    IntervalSets = "8197267c-284f-5f27-9208-e0e47529a953"
    LinearAlgebra = "37e2e46d-f89d-539d-b4ee-838fcccc9c8e"
    StaticArrays = "90137ffa-7385-5640-81b9-e52037218182"

[[deps.Contour]]
git-tree-sha1 = "439e35b0b36e2e5881738abc8857bd92ad6ff9a8"
uuid = "d38c429a-6771-53c6-b99e-75d170b6e991"
version = "0.6.3"

[[deps.CpuId]]
deps = ["Markdown"]
git-tree-sha1 = "fcbb72b032692610bfbdb15018ac16a36cf2e406"
uuid = "adafc99b-e345-5852-983c-f28acb93d879"
version = "0.3.1"

[[deps.Crayons]]
git-tree-sha1 = "249fe38abf76d48563e2f4556bebd215aa317e15"
uuid = "a8cc5b0e-0ffa-5ad4-8c14-923d3ee1735f"
version = "4.1.1"

[[deps.DSP]]
deps = ["Bessels", "FFTW", "IterTools", "LinearAlgebra", "Polynomials", "Random", "Reexport", "SpecialFunctions", "Statistics"]
git-tree-sha1 = "489db9d78b53e44fb753d225c58832632d74ab10"
uuid = "717857b8-e6f2-59f4-9121-6e50c889abd2"
version = "0.8.0"
weakdeps = ["OffsetArrays"]

    [deps.DSP.extensions]
    OffsetArraysExt = "OffsetArrays"

[[deps.DataAPI]]
git-tree-sha1 = "abe83f3a2f1b857aac70ef8b269080af17764bbe"
uuid = "9a962f9c-6df0-11e9-0e5d-c546b8b5ee8a"
version = "1.16.0"

[[deps.DataFrames]]
deps = ["Compat", "DataAPI", "DataStructures", "Future", "InlineStrings", "InvertedIndices", "IteratorInterfaceExtensions", "LinearAlgebra", "Markdown", "Missings", "PooledArrays", "PrecompileTools", "PrettyTables", "Printf", "Random", "Reexport", "SentinelArrays", "SortingAlgorithms", "Statistics", "TableTraits", "Tables", "Unicode"]
git-tree-sha1 = "fb61b4812c49343d7ef0b533ba982c46021938a6"
uuid = "a93c6f00-e57d-5684-b7b6-d8193f3e46c0"
version = "1.7.0"

[[deps.DataStructures]]
deps = ["Compat", "InteractiveUtils", "OrderedCollections"]
git-tree-sha1 = "1d0a14036acb104d9e89698bd408f63ab58cdc82"
uuid = "864edb3b-99cc-5e75-8d2d-829cb0a9cfe8"
version = "0.18.20"

[[deps.DataValueInterfaces]]
git-tree-sha1 = "bfc1187b79289637fa0ef6d4436ebdfe6905cbd6"
uuid = "e2d170a0-9d28-54be-80f0-106bbe20a464"
version = "1.0.0"

[[deps.Dates]]
deps = ["Printf"]
uuid = "ade2ca70-3891-5945-98fb-dc099432e06a"
version = "1.11.0"

[[deps.Dbus_jll]]
deps = ["Artifacts", "Expat_jll", "JLLWrappers", "Libdl"]
git-tree-sha1 = "fc173b380865f70627d7dd1190dc2fce6cc105af"
uuid = "ee1fde0b-3d02-5ea6-8484-8dfef6360eab"
version = "1.14.10+0"

[[deps.DelimitedFiles]]
deps = ["Mmap"]
git-tree-sha1 = "9e2f36d3c96a820c678f2f1f1782582fcf685bae"
uuid = "8bb1440f-4735-579b-a4ab-409b98df4dab"
version = "1.9.1"

[[deps.Distances]]
deps = ["LinearAlgebra", "Statistics", "StatsAPI"]
git-tree-sha1 = "c7e3a542b999843086e2f29dac96a618c105be1d"
uuid = "b4f34e82-e78d-54a5-968a-f98e89d6e8f7"
version = "0.10.12"

    [deps.Distances.extensions]
    DistancesChainRulesCoreExt = "ChainRulesCore"
    DistancesSparseArraysExt = "SparseArrays"

    [deps.Distances.weakdeps]
    ChainRulesCore = "d360d2e6-b24c-11e9-a2a3-2a2ae2dbcce4"
    SparseArrays = "2f01184e-e22b-5df5-ae63-d93ebab69eaf"

[[deps.Distributed]]
deps = ["Random", "Serialization", "Sockets"]
uuid = "8ba89e20-285c-5b6f-9357-94700520ee1b"
version = "1.11.0"

[[deps.DocStringExtensions]]
deps = ["LibGit2"]
git-tree-sha1 = "2fb1e02f2b635d0845df5d7c167fec4dd739b00d"
uuid = "ffbed154-4ef7-542d-bbb7-c09d3a79fcae"
version = "0.9.3"

[[deps.Downloads]]
deps = ["ArgTools", "FileWatching", "LibCURL", "NetworkOptions"]
uuid = "f43a241f-c20a-4ad4-852c-f6b1247861c6"
version = "1.6.0"

[[deps.DynamicAxisWarping]]
deps = ["BinDeps", "DataStructures", "DelimitedFiles", "Distances", "FillArrays", "LinearAlgebra", "LoopVectorization", "ProgressMeter", "RecipesBase", "Requires", "SlidingDistancesBase", "Statistics", "StatsBase", "UnPack"]
git-tree-sha1 = "7a75a8208d92c72f0f30998f04a60cd9a48f9a48"
uuid = "aaaaaaaa-4a10-5553-b683-e707b00e83ce"
version = "0.4.18"

[[deps.EpollShim_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "8a4be429317c42cfae6a7fc03c31bad1970c310d"
uuid = "2702e6a9-849d-5ed8-8c21-79e8b8f9ee43"
version = "0.0.20230411+1"

[[deps.ExceptionUnwrapping]]
deps = ["Test"]
git-tree-sha1 = "d36f682e590a83d63d1c7dbd287573764682d12a"
uuid = "460bff9d-24e4-43bc-9d9f-a8973cb893f4"
version = "0.1.11"

[[deps.Expat_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "d55dffd9ae73ff72f1c0482454dcf2ec6c6c4a63"
uuid = "2e619515-83b5-522b-bb60-26c02a35a201"
version = "2.6.5+0"

[[deps.FFMPEG]]
deps = ["FFMPEG_jll"]
git-tree-sha1 = "53ebe7511fa11d33bec688a9178fac4e49eeee00"
uuid = "c87230d0-a227-11e9-1b43-d7ebe4e7570a"
version = "0.4.2"

[[deps.FFMPEG_jll]]
deps = ["Artifacts", "Bzip2_jll", "FreeType2_jll", "FriBidi_jll", "JLLWrappers", "LAME_jll", "Libdl", "Ogg_jll", "OpenSSL_jll", "Opus_jll", "PCRE2_jll", "Zlib_jll", "libaom_jll", "libass_jll", "libfdk_aac_jll", "libvorbis_jll", "x264_jll", "x265_jll"]
git-tree-sha1 = "466d45dc38e15794ec7d5d63ec03d776a9aff36e"
uuid = "b22a6f82-2f65-5046-a5b2-351ab43fb4e5"
version = "4.4.4+1"

[[deps.FFTW]]
deps = ["AbstractFFTs", "FFTW_jll", "LinearAlgebra", "MKL_jll", "Preferences", "Reexport"]
git-tree-sha1 = "7de7c78d681078f027389e067864a8d53bd7c3c9"
uuid = "7a1cc6ca-52ef-59f5-83cd-3a7055c09341"
version = "1.8.1"

[[deps.FFTW_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "6d6219a004b8cf1e0b4dbe27a2860b8e04eba0be"
uuid = "f5851436-0d7a-5f13-b9de-f02708fd171a"
version = "3.3.11+0"

[[deps.FilePathsBase]]
deps = ["Compat", "Dates"]
git-tree-sha1 = "2ec417fc319faa2d768621085cc1feebbdee686b"
uuid = "48062228-2e41-5def-b9a4-89aafe57970f"
version = "0.9.23"
weakdeps = ["Mmap", "Test"]

    [deps.FilePathsBase.extensions]
    FilePathsBaseMmapExt = "Mmap"
    FilePathsBaseTestExt = "Test"

[[deps.FileWatching]]
uuid = "7b1f6079-737a-58dc-b8bc-7a2ca5c1b5ee"
version = "1.11.0"

[[deps.FillArrays]]
deps = ["LinearAlgebra"]
git-tree-sha1 = "6a70198746448456524cb442b8af316927ff3e1a"
uuid = "1a297f60-69ca-5386-bcde-b61e274b549b"
version = "1.13.0"

    [deps.FillArrays.extensions]
    FillArraysPDMatsExt = "PDMats"
    FillArraysSparseArraysExt = "SparseArrays"
    FillArraysStatisticsExt = "Statistics"

    [deps.FillArrays.weakdeps]
    PDMats = "90014a1f-27ba-587c-ab20-58faa44d9150"
    SparseArrays = "2f01184e-e22b-5df5-ae63-d93ebab69eaf"
    Statistics = "10745b16-79ce-11e8-11f9-7d13ad32a3b2"

[[deps.FixedPointNumbers]]
deps = ["Statistics"]
git-tree-sha1 = "05882d6995ae5c12bb5f36dd2ed3f61c98cbb172"
uuid = "53c48c17-4a7d-5ca2-90c5-79b7896eea93"
version = "0.8.5"

[[deps.Fontconfig_jll]]
deps = ["Artifacts", "Bzip2_jll", "Expat_jll", "FreeType2_jll", "JLLWrappers", "Libdl", "Libuuid_jll", "Zlib_jll"]
git-tree-sha1 = "21fac3c77d7b5a9fc03b0ec503aa1a6392c34d2b"
uuid = "a3f928ae-7b40-5064-980b-68af3947d34b"
version = "2.15.0+0"

[[deps.Format]]
git-tree-sha1 = "9c68794ef81b08086aeb32eeaf33531668d5f5fc"
uuid = "1fa38f19-a742-5d3f-a2b9-30dd87b9d5f8"
version = "1.3.7"

[[deps.FreeType2_jll]]
deps = ["Artifacts", "Bzip2_jll", "JLLWrappers", "Libdl", "Zlib_jll"]
git-tree-sha1 = "786e968a8d2fb167f2e4880baba62e0e26bd8e4e"
uuid = "d7e528f0-a631-5988-bf34-fe36492bcfd7"
version = "2.13.3+1"

[[deps.FriBidi_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "846f7026a9decf3679419122b49f8a1fdb48d2d5"
uuid = "559328eb-81f9-559d-9380-de523a88c83c"
version = "1.0.16+0"

[[deps.Future]]
deps = ["Random"]
uuid = "9fa8497b-333b-5362-9e8d-4d0656e87820"
version = "1.11.0"

[[deps.GLFW_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Libglvnd_jll", "Xorg_libXcursor_jll", "Xorg_libXi_jll", "Xorg_libXinerama_jll", "Xorg_libXrandr_jll", "libdecor_jll", "xkbcommon_jll"]
git-tree-sha1 = "fcb0584ff34e25155876418979d4c8971243bb89"
uuid = "0656b61e-2033-5cc2-a64a-77c0f6c09b89"
version = "3.4.0+2"

[[deps.GR]]
deps = ["Artifacts", "Base64", "DelimitedFiles", "Downloads", "GR_jll", "HTTP", "JSON", "Libdl", "LinearAlgebra", "Preferences", "Printf", "Qt6Wayland_jll", "Random", "Serialization", "Sockets", "TOML", "Tar", "Test", "p7zip_jll"]
git-tree-sha1 = "0ff136326605f8e06e9bcf085a356ab312eef18a"
uuid = "28b8d3ca-fb5f-59d9-8090-bfdbd6d07a71"
version = "0.73.13"

[[deps.GR_jll]]
deps = ["Artifacts", "Bzip2_jll", "Cairo_jll", "FFMPEG_jll", "Fontconfig_jll", "FreeType2_jll", "GLFW_jll", "JLLWrappers", "JpegTurbo_jll", "Libdl", "Libtiff_jll", "Pixman_jll", "Qt6Base_jll", "Zlib_jll", "libpng_jll"]
git-tree-sha1 = "9cb62849057df859575fc1dda1e91b82f8609709"
uuid = "d2c73de3-f751-5644-a686-071e5b155ba9"
version = "0.73.13+0"

[[deps.Gettext_jll]]
deps = ["Artifacts", "CompilerSupportLibraries_jll", "JLLWrappers", "Libdl", "Libiconv_jll", "Pkg", "XML2_jll"]
git-tree-sha1 = "9b02998aba7bf074d14de89f9d37ca24a1a0b046"
uuid = "78b55507-aeef-58d4-861c-77aaff3498b1"
version = "0.21.0+0"

[[deps.Glib_jll]]
deps = ["Artifacts", "Gettext_jll", "JLLWrappers", "Libdl", "Libffi_jll", "Libiconv_jll", "Libmount_jll", "PCRE2_jll", "Zlib_jll"]
git-tree-sha1 = "b0036b392358c80d2d2124746c2bf3d48d457938"
uuid = "7746bdde-850d-59dc-9ae8-88ece973131d"
version = "2.82.4+0"

[[deps.Graphite2_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg"]
git-tree-sha1 = "01979f9b37367603e2848ea225918a3b3861b606"
uuid = "3b182d85-2403-5c21-9c21-1e1f0cc25472"
version = "1.3.14+1"

[[deps.Grisu]]
git-tree-sha1 = "53bb909d1151e57e2484c3d1b53e19552b887fb2"
uuid = "42e2da0e-8278-4e71-bc24-59509adca0fe"
version = "1.0.2"

[[deps.HTTP]]
deps = ["Base64", "CodecZlib", "ConcurrentUtilities", "Dates", "ExceptionUnwrapping", "Logging", "LoggingExtras", "MbedTLS", "NetworkOptions", "OpenSSL", "PrecompileTools", "Random", "SimpleBufferStream", "Sockets", "URIs", "UUIDs"]
git-tree-sha1 = "c67b33b085f6e2faf8bf79a61962e7339a81129c"
uuid = "cd3eb016-35fb-5094-929b-558a96fad6f3"
version = "1.10.15"

[[deps.HarfBuzz_jll]]
deps = ["Artifacts", "Cairo_jll", "Fontconfig_jll", "FreeType2_jll", "Glib_jll", "Graphite2_jll", "JLLWrappers", "Libdl", "Libffi_jll"]
git-tree-sha1 = "55c53be97790242c29031e5cd45e8ac296dadda3"
uuid = "2e76f6c2-a576-52d4-95c1-20adfe4de566"
version = "8.5.0+0"

[[deps.HostCPUFeatures]]
deps = ["BitTwiddlingConvenienceFunctions", "IfElse", "Libdl", "Static"]
git-tree-sha1 = "8e070b599339d622e9a081d17230d74a5c473293"
uuid = "3e5b6fbb-0976-4d2c-9146-d79de83f2fb0"
version = "0.1.17"

[[deps.Hyperscript]]
deps = ["Test"]
git-tree-sha1 = "179267cfa5e712760cd43dcae385d7ea90cc25a4"
uuid = "47d2ed2b-36de-50cf-bf87-49c2cf4b8b91"
version = "0.0.5"

[[deps.HypertextLiteral]]
deps = ["Tricks"]
git-tree-sha1 = "7134810b1afce04bbc1045ca1985fbe81ce17653"
uuid = "ac1192a8-f4b3-4bfe-ba22-af5b92cd3ab2"
version = "0.9.5"

[[deps.IOCapture]]
deps = ["Logging", "Random"]
git-tree-sha1 = "b6d6bfdd7ce25b0f9b2f6b3dd56b2673a66c8770"
uuid = "b5f81e59-6552-4d32-b1f0-c071b021bf89"
version = "0.2.5"

[[deps.IfElse]]
git-tree-sha1 = "debdd00ffef04665ccbb3e150747a77560e8fad1"
uuid = "615f187c-cbe4-4ef1-ba3b-2fcf58d6d173"
version = "0.1.1"

[[deps.InlineStrings]]
git-tree-sha1 = "6a9fde685a7ac1eb3495f8e812c5a7c3711c2d5e"
uuid = "842dd82b-1e85-43dc-bf29-5d0ee9dffc48"
version = "1.4.3"

    [deps.InlineStrings.extensions]
    ArrowTypesExt = "ArrowTypes"
    ParsersExt = "Parsers"

    [deps.InlineStrings.weakdeps]
    ArrowTypes = "31f734f8-188a-4ce0-8406-c8a06bd891cd"
    Parsers = "69de0a69-1ddd-5017-9359-2bf0b02dc9f0"

[[deps.IntelOpenMP_jll]]
deps = ["Artifacts", "JLLWrappers", "LazyArtifacts", "Libdl"]
git-tree-sha1 = "0f14a5456bdc6b9731a5682f439a672750a09e48"
uuid = "1d5cc7b8-4909-519e-a0f8-d0f5ad9712d0"
version = "2025.0.4+0"

[[deps.InteractiveUtils]]
deps = ["Markdown"]
uuid = "b77e0a4c-d291-57a0-90e8-8db25a27a240"
version = "1.11.0"

[[deps.InvertedIndices]]
git-tree-sha1 = "6da3c4316095de0f5ee2ebd875df8721e7e0bdbe"
uuid = "41ab1584-1d38-5bbf-9106-f11c6c58b48f"
version = "1.3.1"

[[deps.IrrationalConstants]]
git-tree-sha1 = "e2222959fbc6c19554dc15174c81bf7bf3aa691c"
uuid = "92d709cd-6900-40b7-9082-c6be49f344b6"
version = "0.2.4"

[[deps.IterTools]]
git-tree-sha1 = "42d5f897009e7ff2cf88db414a389e5ed1bdd023"
uuid = "c8e1da08-722c-5040-9ed9-7db0dc04731e"
version = "1.10.0"

[[deps.IteratorInterfaceExtensions]]
git-tree-sha1 = "a3f24677c21f5bbe9d2a714f95dcd58337fb2856"
uuid = "*************-5014-852e-03e436cf321d"
version = "1.0.0"

[[deps.JLFzf]]
deps = ["Pipe", "REPL", "Random", "fzf_jll"]
git-tree-sha1 = "71b48d857e86bf7a1838c4736545699974ce79a2"
uuid = "1019f520-868f-41f5-a6de-eb00f4b6a39c"
version = "0.1.9"

[[deps.JLLWrappers]]
deps = ["Artifacts", "Preferences"]
git-tree-sha1 = "a007feb38b422fbdab534406aeca1b86823cb4d6"
uuid = "692b3bcd-3c85-4b1f-b108-f13ce0eb3210"
version = "1.7.0"

[[deps.JSON]]
deps = ["Dates", "Mmap", "Parsers", "Unicode"]
git-tree-sha1 = "31e996f0a15c7b280ba9f76636b3ff9e2ae58c9a"
uuid = "682c06a0-de6a-54ab-a142-c8b1cf79cde6"
version = "0.21.4"

[[deps.JpegTurbo_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "eac1206917768cb54957c65a615460d87b455fc1"
uuid = "aacddb02-875f-59d6-b918-886e6ef4fbf8"
version = "3.1.1+0"

[[deps.LAME_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "170b660facf5df5de098d866564877e119141cbd"
uuid = "c1c5ebd0-6772-5130-a774-d5fcae4a789d"
version = "3.100.2+0"

[[deps.LERC_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "aaafe88dccbd957a8d82f7d05be9b69172e0cee3"
uuid = "88015f11-f218-50d7-93a8-a6af411a945d"
version = "4.0.1+0"

[[deps.LLVMOpenMP_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "78211fb6cbc872f77cad3fc0b6cf647d923f4929"
uuid = "1d63c593-3942-5779-bab2-d838dc0a180e"
version = "18.1.7+0"

[[deps.LZO_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "1c602b1127f4751facb671441ca72715cc95938a"
uuid = "dd4b983a-f0e5-5f8d-a1b7-129d4a5fb1ac"
version = "2.10.3+0"

[[deps.LaTeXStrings]]
git-tree-sha1 = "dda21b8cbd6a6c40d9d02a73230f9d70fed6918c"
uuid = "b964fa9f-0449-5b57-a5c2-d3ea65f4040f"
version = "1.4.0"

[[deps.Latexify]]
deps = ["Format", "InteractiveUtils", "LaTeXStrings", "MacroTools", "Markdown", "OrderedCollections", "Requires"]
git-tree-sha1 = "cd714447457c660382fe634710fb56eb255ee42e"
uuid = "23fbe1c1-3f47-55db-b15f-69d7ec21a316"
version = "0.16.6"

    [deps.Latexify.extensions]
    DataFramesExt = "DataFrames"
    SparseArraysExt = "SparseArrays"
    SymEngineExt = "SymEngine"

    [deps.Latexify.weakdeps]
    DataFrames = "a93c6f00-e57d-5684-b7b6-d8193f3e46c0"
    SparseArrays = "2f01184e-e22b-5df5-ae63-d93ebab69eaf"
    SymEngine = "123dc426-2d89-5057-bbad-38513e3affd8"

[[deps.LayoutPointers]]
deps = ["ArrayInterface", "LinearAlgebra", "ManualMemory", "SIMDTypes", "Static", "StaticArrayInterface"]
git-tree-sha1 = "a9eaadb366f5493a5654e843864c13d8b107548c"
uuid = "10f19ff3-798f-405d-979b-55457f8fc047"
version = "0.1.17"

[[deps.LazyArtifacts]]
deps = ["Artifacts", "Pkg"]
uuid = "4af54fe1-eca0-43a8-85a7-787d91b784e3"
version = "1.11.0"

[[deps.LibCURL]]
deps = ["LibCURL_jll", "MozillaCACerts_jll"]
uuid = "b27032c2-a3e7-50c8-80cd-2d36dbcbfd21"
version = "0.6.4"

[[deps.LibCURL_jll]]
deps = ["Artifacts", "LibSSH2_jll", "Libdl", "MbedTLS_jll", "Zlib_jll", "nghttp2_jll"]
uuid = "deac9b47-8bc7-5906-a0fe-35ac56dc84c0"
version = "8.6.0+0"

[[deps.LibGit2]]
deps = ["Base64", "LibGit2_jll", "NetworkOptions", "Printf", "SHA"]
uuid = "76f85450-5226-5b5a-8eaa-529ad045b433"
version = "1.11.0"

[[deps.LibGit2_jll]]
deps = ["Artifacts", "LibSSH2_jll", "Libdl", "MbedTLS_jll"]
uuid = "e37daf67-58a4-590a-8e99-b0245dd2ffc5"
version = "1.7.2+0"

[[deps.LibSSH2_jll]]
deps = ["Artifacts", "Libdl", "MbedTLS_jll"]
uuid = "29816b5a-b9ab-546f-933c-edad1886dfa8"
version = "1.11.0+1"

[[deps.Libdl]]
uuid = "8f399da3-3557-5675-b5ff-fb832c97cbdb"
version = "1.11.0"

[[deps.Libffi_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg"]
git-tree-sha1 = "27ecae93dd25ee0909666e6835051dd684cc035e"
uuid = "e9f186c6-92d2-5b65-8a66-fee21dc1b490"
version = "3.2.2+2"

[[deps.Libgcrypt_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Libgpg_error_jll"]
git-tree-sha1 = "8be878062e0ffa2c3f67bb58a595375eda5de80b"
uuid = "d4300ac3-e22c-5743-9152-c294e39db1e4"
version = "1.11.0+0"

[[deps.Libglvnd_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libX11_jll", "Xorg_libXext_jll"]
git-tree-sha1 = "ff3b4b9d35de638936a525ecd36e86a8bb919d11"
uuid = "7e76a0d4-f3c7-5321-8279-8d96eeed0f29"
version = "1.7.0+0"

[[deps.Libgpg_error_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "df37206100d39f79b3376afb6b9cee4970041c61"
uuid = "7add5ba3-2f88-524e-9cd5-f83b8a55f7b8"
version = "1.51.1+0"

[[deps.Libiconv_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "be484f5c92fad0bd8acfef35fe017900b0b73809"
uuid = "94ce4f54-9a6c-5748-9c1c-f9c7231a4531"
version = "1.18.0+0"

[[deps.Libmount_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "89211ea35d9df5831fca5d33552c02bd33878419"
uuid = "4b2f31a3-9ecc-558c-b454-b3730dcb73e9"
version = "2.40.3+0"

[[deps.Libtiff_jll]]
deps = ["Artifacts", "JLLWrappers", "JpegTurbo_jll", "LERC_jll", "Libdl", "XZ_jll", "Zlib_jll", "Zstd_jll"]
git-tree-sha1 = "4ab7581296671007fc33f07a721631b8855f4b1d"
uuid = "89763e89-9b03-5906-acba-b20f662cd828"
version = "4.7.1+0"

[[deps.Libuuid_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "e888ad02ce716b319e6bdb985d2ef300e7089889"
uuid = "38a345b3-de98-5d2b-a5d3-14cd9215e700"
version = "2.40.3+0"

[[deps.LinearAlgebra]]
deps = ["Libdl", "OpenBLAS_jll", "libblastrampoline_jll"]
uuid = "37e2e46d-f89d-539d-b4ee-838fcccc9c8e"
version = "1.11.0"

[[deps.LogExpFunctions]]
deps = ["DocStringExtensions", "IrrationalConstants", "LinearAlgebra"]
git-tree-sha1 = "13ca9e2586b89836fd20cccf56e57e2b9ae7f38f"
uuid = "2ab3a3ac-af41-5b50-aa03-7779005ae688"
version = "0.3.29"

    [deps.LogExpFunctions.extensions]
    LogExpFunctionsChainRulesCoreExt = "ChainRulesCore"
    LogExpFunctionsChangesOfVariablesExt = "ChangesOfVariables"
    LogExpFunctionsInverseFunctionsExt = "InverseFunctions"

    [deps.LogExpFunctions.weakdeps]
    ChainRulesCore = "d360d2e6-b24c-11e9-a2a3-2a2ae2dbcce4"
    ChangesOfVariables = "9e997f8a-9a97-42d5-a9f1-ce6bfc15e2c0"
    InverseFunctions = "3587e190-3f89-42d0-90ee-14403ec27112"

[[deps.Logging]]
uuid = "56ddb016-857b-54e1-b83d-db4d58db5568"
version = "1.11.0"

[[deps.LoggingExtras]]
deps = ["Dates", "Logging"]
git-tree-sha1 = "f02b56007b064fbfddb4c9cd60161b6dd0f40df3"
uuid = "e6f89c97-d47a-5376-807f-9c37f3926c36"
version = "1.1.0"

[[deps.LoopVectorization]]
deps = ["ArrayInterface", "CPUSummary", "CloseOpenIntervals", "DocStringExtensions", "HostCPUFeatures", "IfElse", "LayoutPointers", "LinearAlgebra", "OffsetArrays", "PolyesterWeave", "PrecompileTools", "SIMDTypes", "SLEEFPirates", "Static", "StaticArrayInterface", "ThreadingUtilities", "UnPack", "VectorizationBase"]
git-tree-sha1 = "e5afce7eaf5b5ca0d444bcb4dc4fd78c54cbbac0"
uuid = "bdcacae8-1622-11e9-2a5c-************"
version = "0.12.172"

    [deps.LoopVectorization.extensions]
    ForwardDiffExt = ["ChainRulesCore", "ForwardDiff"]
    SpecialFunctionsExt = "SpecialFunctions"

    [deps.LoopVectorization.weakdeps]
    ChainRulesCore = "d360d2e6-b24c-11e9-a2a3-2a2ae2dbcce4"
    ForwardDiff = "f6369f11-**************-2563aa707210"
    SpecialFunctions = "276daf66-3868-5448-9aa4-cd146d93841b"

[[deps.MIMEs]]
git-tree-sha1 = "1833212fd6f580c20d4291da9c1b4e8a655b128e"
uuid = "6c6e2e6c-3030-632d-7369-2d6c69616d65"
version = "1.0.0"

[[deps.MKL_jll]]
deps = ["Artifacts", "IntelOpenMP_jll", "JLLWrappers", "LazyArtifacts", "Libdl", "oneTBB_jll"]
git-tree-sha1 = "5de60bc6cb3899cd318d80d627560fae2e2d99ae"
uuid = "856f044c-d86e-5d09-b602-aeab76dc8ba7"
version = "2025.0.1+1"

[[deps.MacroTools]]
git-tree-sha1 = "72aebe0b5051e5143a079a4685a46da330a40472"
uuid = "1914dd2f-81c6-5fcd-8719-6d5c9610ff09"
version = "0.5.15"

[[deps.ManualMemory]]
git-tree-sha1 = "bcaef4fc7a0cfe2cba636d84cda54b5e4e4ca3cd"
uuid = "d125e4d3-2237-4719-b19c-fa641b8a4667"
version = "0.1.8"

[[deps.Markdown]]
deps = ["Base64"]
uuid = "d6f4376e-aef5-505a-96c1-9c027394607a"
version = "1.11.0"

[[deps.MbedTLS]]
deps = ["Dates", "MbedTLS_jll", "MozillaCACerts_jll", "NetworkOptions", "Random", "Sockets"]
git-tree-sha1 = "c067a280ddc25f196b5e7df3877c6b226d390aaf"
uuid = "739be429-bea8-5141-9913-cc70e7f3736d"
version = "1.1.9"

[[deps.MbedTLS_jll]]
deps = ["Artifacts", "Libdl"]
uuid = "c8ffd9c3-330d-5841-b78e-0817d7145fa1"
version = "2.28.6+0"

[[deps.Measures]]
git-tree-sha1 = "c13304c81eec1ed3af7fc20e75fb6b26092a1102"
uuid = "442fdcdd-2543-5da2-b0f3-8c86c306513e"
version = "0.3.2"

[[deps.Missings]]
deps = ["DataAPI"]
git-tree-sha1 = "ec4f7fbeab05d7747bdf98eb74d130a2a2ed298d"
uuid = "e1d29d7a-bbdc-5cf2-9ac0-f12de2c33e28"
version = "1.2.0"

[[deps.Mmap]]
uuid = "a63ad114-7e13-5084-954f-fe012c677804"
version = "1.11.0"

[[deps.MozillaCACerts_jll]]
uuid = "14a3606d-f60d-562e-9121-12d972cd8159"
version = "2023.12.12"

[[deps.NaNMath]]
deps = ["OpenLibm_jll"]
git-tree-sha1 = "cc0a5deefdb12ab3a096f00a6d42133af4560d71"
uuid = "77ba4419-2d1f-58cd-9bb1-8ffee604a2e3"
version = "1.1.2"

[[deps.NetworkOptions]]
uuid = "ca575930-c2e3-43a9-ace4-1e988b2c1908"
version = "1.2.0"

[[deps.OffsetArrays]]
git-tree-sha1 = "a414039192a155fb38c4599a60110f0018c6ec82"
uuid = "6fe1bfb0-de20-5000-8ca7-80f57d26f881"
version = "1.16.0"
weakdeps = ["Adapt"]

    [deps.OffsetArrays.extensions]
    OffsetArraysAdaptExt = "Adapt"

[[deps.Ogg_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg"]
git-tree-sha1 = "887579a3eb005446d514ab7aeac5d1d027658b8f"
uuid = "e7412a2a-1a6e-54c0-be00-318e2571c051"
version = "1.3.5+1"

[[deps.OpenBLAS_jll]]
deps = ["Artifacts", "CompilerSupportLibraries_jll", "Libdl"]
uuid = "4536629a-c528-5b80-bd46-f80d51c5b363"
version = "0.3.27+1"

[[deps.OpenLibm_jll]]
deps = ["Artifacts", "Libdl"]
uuid = "05823500-19ac-5b8b-9628-191a04bc5112"
version = "0.8.1+2"

[[deps.OpenSSL]]
deps = ["BitFlags", "Dates", "MozillaCACerts_jll", "OpenSSL_jll", "Sockets"]
git-tree-sha1 = "38cb508d080d21dc1128f7fb04f20387ed4c0af4"
uuid = "4d8831e6-92b7-49fb-bdf8-b643e874388c"
version = "1.4.3"

[[deps.OpenSSL_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "a9697f1d06cc3eb3fb3ad49cc67f2cfabaac31ea"
uuid = "458c3c95-2e84-50aa-8efc-19380b2a3a95"
version = "3.0.16+0"

[[deps.OpenSpecFun_jll]]
deps = ["Artifacts", "CompilerSupportLibraries_jll", "JLLWrappers", "Libdl"]
git-tree-sha1 = "1346c9208249809840c91b26703912dff463d335"
uuid = "efe28fd5-8261-553b-a9e1-b2916fc3738e"
version = "0.5.6+0"

[[deps.Opus_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "6703a85cb3781bd5909d48730a67205f3f31a575"
uuid = "91d4177d-7536-5919-b921-800302f37372"
version = "1.3.3+0"

[[deps.OrderedCollections]]
git-tree-sha1 = "cc4054e898b852042d7b503313f7ad03de99c3dd"
uuid = "bac558e1-5e72-5ebc-8fee-abe8a469f55d"
version = "1.8.0"

[[deps.PCRE2_jll]]
deps = ["Artifacts", "Libdl"]
uuid = "efcefdf7-47ab-520b-bdef-62a2eaa19f15"
version = "10.42.0+1"

[[deps.Pango_jll]]
deps = ["Artifacts", "Cairo_jll", "Fontconfig_jll", "FreeType2_jll", "FriBidi_jll", "Glib_jll", "HarfBuzz_jll", "JLLWrappers", "Libdl"]
git-tree-sha1 = "3b31172c032a1def20c98dae3f2cdc9d10e3b561"
uuid = "36c8627f-9965-5494-a995-c6b170f724f3"
version = "1.56.1+0"

[[deps.Parsers]]
deps = ["Dates", "PrecompileTools", "UUIDs"]
git-tree-sha1 = "8489905bcdbcfac64d1daa51ca07c0d8f0283821"
uuid = "69de0a69-1ddd-5017-9359-2bf0b02dc9f0"
version = "2.8.1"

[[deps.Pipe]]
git-tree-sha1 = "6842804e7867b115ca9de748a0cf6b364523c16d"
uuid = "b98c9c47-44ae-5843-9183-064241ee97a0"
version = "1.3.0"

[[deps.Pixman_jll]]
deps = ["Artifacts", "CompilerSupportLibraries_jll", "JLLWrappers", "LLVMOpenMP_jll", "Libdl"]
git-tree-sha1 = "35621f10a7531bc8fa58f74610b1bfb70a3cfc6b"
uuid = "30392449-352a-5448-841d-b1acce4e97dc"
version = "0.43.4+0"

[[deps.Pkg]]
deps = ["Artifacts", "Dates", "Downloads", "FileWatching", "LibGit2", "Libdl", "Logging", "Markdown", "Printf", "Random", "SHA", "TOML", "Tar", "UUIDs", "p7zip_jll"]
uuid = "44cfe95a-1eb2-52ea-b672-e2afdf69b78f"
version = "1.11.0"
weakdeps = ["REPL"]

    [deps.Pkg.extensions]
    REPLExt = "REPL"

[[deps.PlotThemes]]
deps = ["PlotUtils", "Statistics"]
git-tree-sha1 = "41031ef3a1be6f5bbbf3e8073f210556daeae5ca"
uuid = "ccf2f8ad-2431-5c83-bf29-c5338b663b6a"
version = "3.3.0"

[[deps.PlotUtils]]
deps = ["ColorSchemes", "Colors", "Dates", "PrecompileTools", "Printf", "Random", "Reexport", "StableRNGs", "Statistics"]
git-tree-sha1 = "3ca9a356cd2e113c420f2c13bea19f8d3fb1cb18"
uuid = "995b91a9-d308-5afd-9ec6-746e21dbc043"
version = "1.4.3"

[[deps.Plots]]
deps = ["Base64", "Contour", "Dates", "Downloads", "FFMPEG", "FixedPointNumbers", "GR", "JLFzf", "JSON", "LaTeXStrings", "Latexify", "LinearAlgebra", "Measures", "NaNMath", "Pkg", "PlotThemes", "PlotUtils", "PrecompileTools", "Printf", "REPL", "Random", "RecipesBase", "RecipesPipeline", "Reexport", "RelocatableFolders", "Requires", "Scratch", "Showoff", "SparseArrays", "Statistics", "StatsBase", "TOML", "UUIDs", "UnicodeFun", "UnitfulLatexify", "Unzip"]
git-tree-sha1 = "dae01f8c2e069a683d3a6e17bbae5070ab94786f"
uuid = "91a5bcdd-55d7-5caf-9e0b-520d859cae80"
version = "1.40.9"

    [deps.Plots.extensions]
    FileIOExt = "FileIO"
    GeometryBasicsExt = "GeometryBasics"
    IJuliaExt = "IJulia"
    ImageInTerminalExt = "ImageInTerminal"
    UnitfulExt = "Unitful"

    [deps.Plots.weakdeps]
    FileIO = "5789e2e9-d7fb-5bc7-8068-2c6fae9b9549"
    GeometryBasics = "5c1252a2-5f33-56bf-86c9-59e7332b4326"
    IJulia = "7073ff75-c697-5162-941a-fcdaad2a7d2a"
    ImageInTerminal = "d8c32880-2388-543b-8c61-d9f865259254"
    Unitful = "1986cc42-f94f-5a68-af5c-568840ba703d"

[[deps.PlutoUI]]
deps = ["AbstractPlutoDingetjes", "Base64", "ColorTypes", "Dates", "FixedPointNumbers", "Hyperscript", "HypertextLiteral", "IOCapture", "InteractiveUtils", "JSON", "Logging", "MIMEs", "Markdown", "Random", "Reexport", "URIs", "UUIDs"]
git-tree-sha1 = "7e71a55b87222942f0f9337be62e26b1f103d3e4"
uuid = "7f904dfe-b85e-4ff6-b463-dae2292396a8"
version = "0.7.61"

[[deps.PolyesterWeave]]
deps = ["BitTwiddlingConvenienceFunctions", "CPUSummary", "IfElse", "Static", "ThreadingUtilities"]
git-tree-sha1 = "645bed98cd47f72f67316fd42fc47dee771aefcd"
uuid = "1d0040c9-8b98-4ee7-8388-3f51789ca0ad"
version = "0.2.2"

[[deps.Polynomials]]
deps = ["LinearAlgebra", "OrderedCollections", "RecipesBase", "Requires", "Setfield", "SparseArrays"]
git-tree-sha1 = "0973615c3239b1b0d173b77befdada6deb5aa9d8"
uuid = "f27b6e38-b328-58d1-80ce-0feddd5e7a45"
version = "4.0.17"

    [deps.Polynomials.extensions]
    PolynomialsChainRulesCoreExt = "ChainRulesCore"
    PolynomialsFFTWExt = "FFTW"
    PolynomialsMakieCoreExt = "MakieCore"
    PolynomialsMutableArithmeticsExt = "MutableArithmetics"

    [deps.Polynomials.weakdeps]
    ChainRulesCore = "d360d2e6-b24c-11e9-a2a3-2a2ae2dbcce4"
    FFTW = "7a1cc6ca-52ef-59f5-83cd-3a7055c09341"
    MakieCore = "20f20a25-4f0e-4fdf-b5d1-57303727442b"
    MutableArithmetics = "d8a4904e-b15c-11e9-3269-09a3773c0cb0"

[[deps.PooledArrays]]
deps = ["DataAPI", "Future"]
git-tree-sha1 = "36d8b4b899628fb92c2749eb488d884a926614d3"
uuid = "2dfb63ee-cc39-5dd5-95bd-886bf059d720"
version = "1.4.3"

[[deps.PrecompileTools]]
deps = ["Preferences"]
git-tree-sha1 = "5aa36f7049a63a1528fe8f7c3f2113413ffd4e1f"
uuid = "aea7be01-6a6a-4083-8856-8a6e6704d82a"
version = "1.2.1"

[[deps.Preferences]]
deps = ["TOML"]
git-tree-sha1 = "9306f6085165d270f7e3db02af26a400d580f5c6"
uuid = "21216c6a-2e73-6563-6e65-************"
version = "1.4.3"

[[deps.PrettyTables]]
deps = ["Crayons", "LaTeXStrings", "Markdown", "PrecompileTools", "Printf", "Reexport", "StringManipulation", "Tables"]
git-tree-sha1 = "1101cd475833706e4d0e7b122218257178f48f34"
uuid = "08abe8d2-0d0c-5749-adfa-8a2ac140af0d"
version = "2.4.0"

[[deps.Printf]]
deps = ["Unicode"]
uuid = "de0858da-6303-5e67-8744-51eddeeeb8d7"
version = "1.11.0"

[[deps.ProgressMeter]]
deps = ["Distributed", "Printf"]
git-tree-sha1 = "8f6bc219586aef8baf0ff9a5fe16ee9c70cb65e4"
uuid = "92933f4c-e287-5a05-a399-4b506db050ca"
version = "1.10.2"

[[deps.PtrArrays]]
git-tree-sha1 = "1d36ef11a9aaf1e8b74dacc6a731dd1de8fd493d"
uuid = "43287f4e-b6f4-7ad1-bb20-aadabca52c3d"
version = "1.3.0"

[[deps.Qt6Base_jll]]
deps = ["Artifacts", "CompilerSupportLibraries_jll", "Fontconfig_jll", "Glib_jll", "JLLWrappers", "Libdl", "Libglvnd_jll", "OpenSSL_jll", "Vulkan_Loader_jll", "Xorg_libSM_jll", "Xorg_libXext_jll", "Xorg_libXrender_jll", "Xorg_libxcb_jll", "Xorg_xcb_util_cursor_jll", "Xorg_xcb_util_image_jll", "Xorg_xcb_util_keysyms_jll", "Xorg_xcb_util_renderutil_jll", "Xorg_xcb_util_wm_jll", "Zlib_jll", "libinput_jll", "xkbcommon_jll"]
git-tree-sha1 = "492601870742dcd38f233b23c3ec629628c1d724"
uuid = "c0090381-4147-56d7-9ebc-da0b1113ec56"
version = "6.7.1+1"

[[deps.Qt6Declarative_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Qt6Base_jll", "Qt6ShaderTools_jll"]
git-tree-sha1 = "e5dd466bf2569fe08c91a2cc29c1003f4797ac3b"
uuid = "629bc702-f1f5-5709-abd5-49b8460ea067"
version = "6.7.1+2"

[[deps.Qt6ShaderTools_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Qt6Base_jll"]
git-tree-sha1 = "1a180aeced866700d4bebc3120ea1451201f16bc"
uuid = "ce943373-25bb-56aa-8eca-768745ed7b5a"
version = "6.7.1+1"

[[deps.Qt6Wayland_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Qt6Base_jll", "Qt6Declarative_jll"]
git-tree-sha1 = "729927532d48cf79f49070341e1d918a65aba6b0"
uuid = "e99dba38-086e-5de3-a5b1-6e4c66e897c3"
version = "6.7.1+1"

[[deps.REPL]]
deps = ["InteractiveUtils", "Markdown", "Sockets", "StyledStrings", "Unicode"]
uuid = "3fa0cd96-eef1-5676-8a61-b3b8758bbffb"
version = "1.11.0"

[[deps.Random]]
deps = ["SHA"]
uuid = "9a3f8284-a2c9-5f02-9a11-845980a1fd5c"
version = "1.11.0"

[[deps.RecipesBase]]
deps = ["PrecompileTools"]
git-tree-sha1 = "5c3d09cc4f31f5fc6af001c250bf1278733100ff"
uuid = "3cdcf5f2-1ef4-517c-9805-6587b60abb01"
version = "1.3.4"

[[deps.RecipesPipeline]]
deps = ["Dates", "NaNMath", "PlotUtils", "PrecompileTools", "RecipesBase"]
git-tree-sha1 = "45cf9fd0ca5839d06ef333c8201714e888486342"
uuid = "01d81517-befc-4cb6-b9ec-a95719d0359c"
version = "0.6.12"

[[deps.Reexport]]
git-tree-sha1 = "45e428421666073eab6f2da5c9d310d99bb12f9b"
uuid = "189a3867-3050-52da-a836-e630ba90ab69"
version = "1.2.2"

[[deps.RelocatableFolders]]
deps = ["SHA", "Scratch"]
git-tree-sha1 = "ffdaf70d81cf6ff22c2b6e733c900c3321cab864"
uuid = "05181044-ff0b-4ac5-8273-598c1e38db00"
version = "1.0.1"

[[deps.Requires]]
deps = ["UUIDs"]
git-tree-sha1 = "62389eeff14780bfe55195b7204c0d8738436d64"
uuid = "ae029012-a4dd-5104-9daa-d747884805df"
version = "1.3.1"

[[deps.SHA]]
uuid = "ea8e919c-243c-51af-8825-aaa63cd721ce"
version = "0.7.0"

[[deps.SIMDTypes]]
git-tree-sha1 = "330289636fb8107c5f32088d2741e9fd7a061a5c"
uuid = "94e857df-77ce-4151-89e5-788b33177be4"
version = "0.1.0"

[[deps.SLEEFPirates]]
deps = ["IfElse", "Static", "VectorizationBase"]
git-tree-sha1 = "456f610ca2fbd1c14f5fcf31c6bfadc55e7d66e0"
uuid = "476501e8-09a2-5ece-8869-fb82de89a1fa"
version = "0.6.43"

[[deps.Scratch]]
deps = ["Dates"]
git-tree-sha1 = "3bac05bc7e74a75fd9cba4295cde4045d9fe2386"
uuid = "6c6a2e73-**************-************"
version = "1.2.1"

[[deps.SentinelArrays]]
deps = ["Dates", "Random"]
git-tree-sha1 = "712fb0231ee6f9120e005ccd56297abbc053e7e0"
uuid = "91c51154-3ec4-41a3-a24f-3f23e20d615c"
version = "1.4.8"

[[deps.Serialization]]
uuid = "9e88b42a-f829-5b0c-bbe9-9e923198166b"
version = "1.11.0"

[[deps.Setfield]]
deps = ["ConstructionBase", "Future", "MacroTools", "StaticArraysCore"]
git-tree-sha1 = "c5391c6ace3bc430ca630251d02ea9687169ca68"
uuid = "efcf1570-3423-57d1-acb7-fd33fddbac46"
version = "1.1.2"

[[deps.Showoff]]
deps = ["Dates", "Grisu"]
git-tree-sha1 = "91eddf657aca81df9ae6ceb20b959ae5653ad1de"
uuid = "992d4aef-0814-514b-bc4d-f2e9a6c4116f"
version = "1.0.3"

[[deps.SimpleBufferStream]]
git-tree-sha1 = "f305871d2f381d21527c770d4788c06c097c9bc1"
uuid = "777ac1f9-54b0-4bf8-805c-2214025038e7"
version = "1.2.0"

[[deps.SlidingDistancesBase]]
deps = ["DSP", "Distances", "DocStringExtensions", "LinearAlgebra", "LoopVectorization", "Statistics"]
git-tree-sha1 = "54edaa4bff5fcc5d3c25d517b9b32e5e93873199"
uuid = "25b0cc0c-38e4-462f-a11d-8564868c562d"
version = "0.3.6"

[[deps.Sockets]]
uuid = "6462fe0b-24de-5631-8697-dd941f90decc"
version = "1.11.0"

[[deps.SortingAlgorithms]]
deps = ["DataStructures"]
git-tree-sha1 = "66e0a8e672a0bdfca2c3f5937efb8538b9ddc085"
uuid = "a2af1166-a08f-5f64-846c-94a0d3cef48c"
version = "1.2.1"

[[deps.SparseArrays]]
deps = ["Libdl", "LinearAlgebra", "Random", "Serialization", "SuiteSparse_jll"]
uuid = "2f01184e-e22b-5df5-ae63-d93ebab69eaf"
version = "1.11.0"

[[deps.SpecialFunctions]]
deps = ["IrrationalConstants", "LogExpFunctions", "OpenLibm_jll", "OpenSpecFun_jll"]
git-tree-sha1 = "64cca0c26b4f31ba18f13f6c12af7c85f478cfde"
uuid = "276daf66-3868-5448-9aa4-cd146d93841b"
version = "2.5.0"

    [deps.SpecialFunctions.extensions]
    SpecialFunctionsChainRulesCoreExt = "ChainRulesCore"

    [deps.SpecialFunctions.weakdeps]
    ChainRulesCore = "d360d2e6-b24c-11e9-a2a3-2a2ae2dbcce4"

[[deps.StableRNGs]]
deps = ["Random"]
git-tree-sha1 = "83e6cce8324d49dfaf9ef059227f91ed4441a8e5"
uuid = "860ef19b-820b-49d6-a774-d7a799459cd3"
version = "1.0.2"

[[deps.Static]]
deps = ["CommonWorldInvalidations", "IfElse", "PrecompileTools"]
git-tree-sha1 = "f737d444cb0ad07e61b3c1bef8eb91203c321eff"
uuid = "aedffcd0-7271-4cad-89d0-dc628f76c6d3"
version = "1.2.0"

[[deps.StaticArrayInterface]]
deps = ["ArrayInterface", "Compat", "IfElse", "LinearAlgebra", "PrecompileTools", "Static"]
git-tree-sha1 = "96381d50f1ce85f2663584c8e886a6ca97e60554"
uuid = "0d7ed370-da01-4f52-bd93-41d350b8b718"
version = "1.8.0"
weakdeps = ["OffsetArrays", "StaticArrays"]

    [deps.StaticArrayInterface.extensions]
    StaticArrayInterfaceOffsetArraysExt = "OffsetArrays"
    StaticArrayInterfaceStaticArraysExt = "StaticArrays"

[[deps.StaticArrays]]
deps = ["LinearAlgebra", "PrecompileTools", "Random", "StaticArraysCore"]
git-tree-sha1 = "0feb6b9031bd5c51f9072393eb5ab3efd31bf9e4"
uuid = "90137ffa-7385-5640-81b9-e52037218182"
version = "1.9.13"

    [deps.StaticArrays.extensions]
    StaticArraysChainRulesCoreExt = "ChainRulesCore"
    StaticArraysStatisticsExt = "Statistics"

    [deps.StaticArrays.weakdeps]
    ChainRulesCore = "d360d2e6-b24c-11e9-a2a3-2a2ae2dbcce4"
    Statistics = "10745b16-79ce-11e8-11f9-7d13ad32a3b2"

[[deps.StaticArraysCore]]
git-tree-sha1 = "192954ef1208c7019899fbf8049e717f92959682"
uuid = "1e83bf80-4336-4d27-bf5d-d5a4f845583c"
version = "1.4.3"

[[deps.Statistics]]
deps = ["LinearAlgebra"]
git-tree-sha1 = "ae3bb1eb3bba077cd276bc5cfc337cc65c3075c0"
uuid = "10745b16-79ce-11e8-11f9-7d13ad32a3b2"
version = "1.11.1"
weakdeps = ["SparseArrays"]

    [deps.Statistics.extensions]
    SparseArraysExt = ["SparseArrays"]

[[deps.StatsAPI]]
deps = ["LinearAlgebra"]
git-tree-sha1 = "1ff449ad350c9c4cbc756624d6f8a8c3ef56d3ed"
uuid = "82ae8749-77ed-4fe6-ae5f-f523153014b0"
version = "1.7.0"

[[deps.StatsBase]]
deps = ["AliasTables", "DataAPI", "DataStructures", "LinearAlgebra", "LogExpFunctions", "Missings", "Printf", "Random", "SortingAlgorithms", "SparseArrays", "Statistics", "StatsAPI"]
git-tree-sha1 = "29321314c920c26684834965ec2ce0dacc9cf8e5"
uuid = "2913bbd2-ae8a-5f71-8c99-4fb6c76f3a91"
version = "0.34.4"

[[deps.StringManipulation]]
deps = ["PrecompileTools"]
git-tree-sha1 = "725421ae8e530ec29bcbdddbe91ff8053421d023"
uuid = "892a3eda-7b42-436c-8928-eab12a02cf0e"
version = "0.4.1"

[[deps.StyledStrings]]
uuid = "f489334b-da3d-4c2e-b8f0-e476e12c162b"
version = "1.11.0"

[[deps.SuiteSparse_jll]]
deps = ["Artifacts", "Libdl", "libblastrampoline_jll"]
uuid = "bea87d4a-7f5b-5778-9afe-8cc45184846c"
version = "7.7.0+0"

[[deps.TOML]]
deps = ["Dates"]
uuid = "fa267f1f-6049-4f14-aa54-33bafae1ed76"
version = "1.0.3"

[[deps.TableTraits]]
deps = ["IteratorInterfaceExtensions"]
git-tree-sha1 = "c06b2f539df1c6efa794486abfb6ed2022561a39"
uuid = "3783bdb8-4a98-5b6b-af9a-565f29a5fe9c"
version = "1.0.1"

[[deps.Tables]]
deps = ["DataAPI", "DataValueInterfaces", "IteratorInterfaceExtensions", "OrderedCollections", "TableTraits"]
git-tree-sha1 = "598cd7c1f68d1e205689b1c2fe65a9f85846f297"
uuid = "bd369af6-aec1-5ad0-b16a-f7cc5008161c"
version = "1.12.0"

[[deps.Tar]]
deps = ["ArgTools", "SHA"]
uuid = "a4e569a6-e804-4fa4-b0f3-eef7a1d5b13e"
version = "1.10.0"

[[deps.TensorCore]]
deps = ["LinearAlgebra"]
git-tree-sha1 = "1feb45f88d133a655e001435632f019a9a1bcdb6"
uuid = "62fd8b95-f654-4bbd-a8a5-9c27f68ccd50"
version = "0.1.1"

[[deps.Test]]
deps = ["InteractiveUtils", "Logging", "Random", "Serialization"]
uuid = "8dfed614-e22c-5e08-85e1-65c5234f0b40"
version = "1.11.0"

[[deps.ThreadSafeDicts]]
git-tree-sha1 = "300b753c0a786ea43fdafc26a4e50b87fb58cd50"
uuid = "4239201d-c60e-5e0a-9702-85d713665ba7"
version = "0.1.6"

[[deps.ThreadingUtilities]]
deps = ["ManualMemory"]
git-tree-sha1 = "eda08f7e9818eb53661b3deb74e3159460dfbc27"
uuid = "8290d209-cae3-49c0-8002-c8c24d57dab5"
version = "0.5.2"

[[deps.TranscodingStreams]]
git-tree-sha1 = "0c45878dcfdcfa8480052b6ab162cdd138781742"
uuid = "3bb67fe8-82b1-5028-8e26-92a6c54297fa"
version = "0.11.3"

[[deps.Tricks]]
git-tree-sha1 = "6cae795a5a9313bbb4f60683f7263318fc7d1505"
uuid = "410a4b4d-49e4-4fbc-ab6d-cb71b17b3775"
version = "0.1.10"

[[deps.URIParser]]
deps = ["Unicode"]
git-tree-sha1 = "53a9f49546b8d2dd2e688d216421d050c9a31d0d"
uuid = "30578b45-9adc-5946-b283-645ec420af67"
version = "0.4.1"

[[deps.URIs]]
git-tree-sha1 = "67db6cc7b3821e19ebe75791a9dd19c9b1188f2b"
uuid = "5c2747f8-b7ea-4ff2-ba2e-563bfd36b1d4"
version = "1.5.1"

[[deps.UUIDs]]
deps = ["Random", "SHA"]
uuid = "cf7118a7-6976-5b1a-9a39-7adc72f591a4"
version = "1.11.0"

[[deps.UnPack]]
git-tree-sha1 = "387c1f73762231e86e0c9c5443ce3b4a0a9a0c2b"
uuid = "3a884ed6-31ef-47d7-9d2a-63182c4928ed"
version = "1.0.2"

[[deps.Unicode]]
uuid = "4ec0a83e-493e-50e2-b9ac-8f72acf5a8f5"
version = "1.11.0"

[[deps.UnicodeFun]]
deps = ["REPL"]
git-tree-sha1 = "53915e50200959667e78a92a418594b428dffddf"
uuid = "1cfade01-22cf-5700-b092-accc4b62d6e1"
version = "0.4.1"

[[deps.Unitful]]
deps = ["Dates", "LinearAlgebra", "Random"]
git-tree-sha1 = "c0667a8e676c53d390a09dc6870b3d8d6650e2bf"
uuid = "1986cc42-f94f-5a68-af5c-568840ba703d"
version = "1.22.0"

    [deps.Unitful.extensions]
    ConstructionBaseUnitfulExt = "ConstructionBase"
    InverseFunctionsUnitfulExt = "InverseFunctions"

    [deps.Unitful.weakdeps]
    ConstructionBase = "187b0558-2788-49d3-abe0-74a17ed4e7c9"
    InverseFunctions = "3587e190-3f89-42d0-90ee-14403ec27112"

[[deps.UnitfulLatexify]]
deps = ["LaTeXStrings", "Latexify", "Unitful"]
git-tree-sha1 = "975c354fcd5f7e1ddcc1f1a23e6e091d99e99bc8"
uuid = "45397f5d-5981-4c77-b2b3-fc36d6e9b728"
version = "1.6.4"

[[deps.Unzip]]
git-tree-sha1 = "ca0969166a028236229f63514992fc073799bb78"
uuid = "41fe7b60-77ed-43a1-b4f0-825fd5a5650d"
version = "0.2.0"

[[deps.VectorizationBase]]
deps = ["ArrayInterface", "CPUSummary", "HostCPUFeatures", "IfElse", "LayoutPointers", "Libdl", "LinearAlgebra", "SIMDTypes", "Static", "StaticArrayInterface"]
git-tree-sha1 = "4ab62a49f1d8d9548a1c8d1a75e5f55cf196f64e"
uuid = "3d5dd08c-fd9d-11e8-17fa-ed2836048c2f"
version = "0.21.71"

[[deps.Vulkan_Loader_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Wayland_jll", "Xorg_libX11_jll", "Xorg_libXrandr_jll", "xkbcommon_jll"]
git-tree-sha1 = "2f0486047a07670caad3a81a075d2e518acc5c59"
uuid = "a44049a8-05dd-5a78-86c9-5fde0876e88c"
version = "1.3.243+0"

[[deps.Wayland_jll]]
deps = ["Artifacts", "EpollShim_jll", "Expat_jll", "JLLWrappers", "Libdl", "Libffi_jll", "Pkg", "XML2_jll"]
git-tree-sha1 = "85c7811eddec9e7f22615371c3cc81a504c508ee"
uuid = "a2964d1f-97da-50d4-b82a-358c7fce9d89"
version = "1.21.0+2"

[[deps.Wayland_protocols_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg"]
git-tree-sha1 = "5db3e9d307d32baba7067b13fc7b5aa6edd4a19a"
uuid = "2381bf8a-dfd0-557d-9999-79630e7b1b91"
version = "1.36.0+0"

[[deps.WeakRefStrings]]
deps = ["DataAPI", "InlineStrings", "Parsers"]
git-tree-sha1 = "b1be2855ed9ed8eac54e5caff2afcdb442d52c23"
uuid = "ea10d353-3f73-51f8-a26c-33c1cb351aa5"
version = "1.4.2"

[[deps.WorkerUtilities]]
git-tree-sha1 = "cd1659ba0d57b71a464a29e64dbc67cfe83d54e7"
uuid = "76eceee3-57b5-4d4a-8e66-0e911cebbf60"
version = "1.6.1"

[[deps.XML2_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Libiconv_jll", "Zlib_jll"]
git-tree-sha1 = "b8b243e47228b4a3877f1dd6aee0c5d56db7fcf4"
uuid = "02c8fc9c-b97f-50b9-bbe4-9be30ff0a78a"
version = "2.13.6+1"

[[deps.XSLT_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Libgcrypt_jll", "Libgpg_error_jll", "Libiconv_jll", "XML2_jll", "Zlib_jll"]
git-tree-sha1 = "7d1671acbe47ac88e981868a078bd6b4e27c5191"
uuid = "aed1982a-8fda-507f-9586-7b0439959a61"
version = "1.1.42+0"

[[deps.XZ_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "56c6604ec8b2d82cc4cfe01aa03b00426aac7e1f"
uuid = "ffd25f8a-64ca-5728-b0f7-c24cf3aae800"
version = "5.6.4+1"

[[deps.Xorg_libICE_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "326b4fea307b0b39892b3e85fa451692eda8d46c"
uuid = "f67eecfb-183a-506d-b269-f58e52b52d7c"
version = "1.1.1+0"

[[deps.Xorg_libSM_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libICE_jll"]
git-tree-sha1 = "3796722887072218eabafb494a13c963209754ce"
uuid = "c834827a-8449-5923-a945-d239c165b7dd"
version = "1.2.4+0"

[[deps.Xorg_libX11_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libxcb_jll", "Xorg_xtrans_jll"]
git-tree-sha1 = "9dafcee1d24c4f024e7edc92603cedba72118283"
uuid = "4f6342f7-b3d2-589e-9d20-edeb45f2b2bc"
version = "1.8.6+3"

[[deps.Xorg_libXau_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "e9216fdcd8514b7072b43653874fd688e4c6c003"
uuid = "0c0b7dd1-d40b-584c-a123-a41640f87eec"
version = "1.0.12+0"

[[deps.Xorg_libXcursor_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libXfixes_jll", "Xorg_libXrender_jll"]
git-tree-sha1 = "807c226eaf3651e7b2c468f687ac788291f9a89b"
uuid = "935fb764-8cf2-53bf-bb30-45bb1f8bf724"
version = "1.2.3+0"

[[deps.Xorg_libXdmcp_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "89799ae67c17caa5b3b5a19b8469eeee474377db"
uuid = "a3789734-cfe1-5b06-b2d0-1dd0d9d62d05"
version = "1.1.5+0"

[[deps.Xorg_libXext_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libX11_jll"]
git-tree-sha1 = "d7155fea91a4123ef59f42c4afb5ab3b4ca95058"
uuid = "1082639a-0dae-5f34-9b06-72781eeb8cb3"
version = "1.3.6+3"

[[deps.Xorg_libXfixes_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libX11_jll"]
git-tree-sha1 = "6fcc21d5aea1a0b7cce6cab3e62246abd1949b86"
uuid = "d091e8ba-531a-589c-9de9-94069b037ed8"
version = "6.0.0+0"

[[deps.Xorg_libXi_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libXext_jll", "Xorg_libXfixes_jll"]
git-tree-sha1 = "984b313b049c89739075b8e2a94407076de17449"
uuid = "a51aa0fd-4e3c-5386-b890-e753decda492"
version = "1.8.2+0"

[[deps.Xorg_libXinerama_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libXext_jll"]
git-tree-sha1 = "a1a7eaf6c3b5b05cb903e35e8372049b107ac729"
uuid = "d1454406-59df-5ea1-beac-c340f2130bc3"
version = "1.1.5+0"

[[deps.Xorg_libXrandr_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libXext_jll", "Xorg_libXrender_jll"]
git-tree-sha1 = "b6f664b7b2f6a39689d822a6300b14df4668f0f4"
uuid = "ec84b674-ba8e-5d96-8ba1-2a689ba10484"
version = "1.5.4+0"

[[deps.Xorg_libXrender_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libX11_jll"]
git-tree-sha1 = "a490c6212a0e90d2d55111ac956f7c4fa9c277a6"
uuid = "ea2f1a96-1ddc-540d-b46f-429655e07cfa"
version = "0.9.11+1"

[[deps.Xorg_libpthread_stubs_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "c57201109a9e4c0585b208bb408bc41d205ac4e9"
uuid = "14d82f49-176c-5ed1-bb49-ad3f5cbd8c74"
version = "0.1.2+0"

[[deps.Xorg_libxcb_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "XSLT_jll", "Xorg_libXau_jll", "Xorg_libXdmcp_jll", "Xorg_libpthread_stubs_jll"]
git-tree-sha1 = "1a74296303b6524a0472a8cb12d3d87a78eb3612"
uuid = "c7cfdc94-dc32-55de-ac96-5a1b8d977c5b"
version = "1.17.0+3"

[[deps.Xorg_libxkbfile_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libX11_jll"]
git-tree-sha1 = "dbc53e4cf7701c6c7047c51e17d6e64df55dca94"
uuid = "cc61e674-0454-545c-8b26-ed2c68acab7a"
version = "1.1.2+1"

[[deps.Xorg_xcb_util_cursor_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_xcb_util_image_jll", "Xorg_xcb_util_jll", "Xorg_xcb_util_renderutil_jll"]
git-tree-sha1 = "04341cb870f29dcd5e39055f895c39d016e18ccd"
uuid = "e920d4aa-a673-5f3a-b3d7-f755a4d47c43"
version = "0.1.4+0"

[[deps.Xorg_xcb_util_image_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg", "Xorg_xcb_util_jll"]
git-tree-sha1 = "0fab0a40349ba1cba2c1da699243396ff8e94b97"
uuid = "12413925-8142-5f55-bb0e-6d7ca50bb09b"
version = "0.4.0+1"

[[deps.Xorg_xcb_util_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg", "Xorg_libxcb_jll"]
git-tree-sha1 = "e7fd7b2881fa2eaa72717420894d3938177862d1"
uuid = "2def613f-5ad1-5310-b15b-b15d46f528f5"
version = "0.4.0+1"

[[deps.Xorg_xcb_util_keysyms_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg", "Xorg_xcb_util_jll"]
git-tree-sha1 = "d1151e2c45a544f32441a567d1690e701ec89b00"
uuid = "975044d2-76e6-5fbe-bf08-97ce7c6574c7"
version = "0.4.0+1"

[[deps.Xorg_xcb_util_renderutil_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg", "Xorg_xcb_util_jll"]
git-tree-sha1 = "dfd7a8f38d4613b6a575253b3174dd991ca6183e"
uuid = "0d47668e-0667-5a69-a72c-f761630bfb7e"
version = "0.3.9+1"

[[deps.Xorg_xcb_util_wm_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg", "Xorg_xcb_util_jll"]
git-tree-sha1 = "e78d10aab01a4a154142c5006ed44fd9e8e31b67"
uuid = "c22f9ab0-d5fe-5066-847c-f4bb1cd4e361"
version = "0.4.1+1"

[[deps.Xorg_xkbcomp_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_libxkbfile_jll"]
git-tree-sha1 = "ab2221d309eda71020cdda67a973aa582aa85d69"
uuid = "35661453-b289-5fab-8a00-3d9160c6a3a4"
version = "1.4.6+1"

[[deps.Xorg_xkeyboard_config_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Xorg_xkbcomp_jll"]
git-tree-sha1 = "691634e5453ad362044e2ad653e79f3ee3bb98c3"
uuid = "33bec58e-1273-512f-9401-5d533626f822"
version = "2.39.0+0"

[[deps.Xorg_xtrans_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "6dba04dbfb72ae3ebe5418ba33d087ba8aa8cb00"
uuid = "c5fb5394-a638-5e4d-96e5-b29de1b5cf10"
version = "1.5.1+0"

[[deps.Zlib_jll]]
deps = ["Libdl"]
uuid = "83775a58-1f1d-513f-b197-d71354ab007a"
version = "1.2.13+1"

[[deps.Zstd_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "446b23e73536f84e8037f5dce465e92275f6a308"
uuid = "3161d3a3-bdf6-5164-811a-617609db77b4"
version = "1.5.7+1"

[[deps.eudev_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg", "gperf_jll"]
git-tree-sha1 = "431b678a28ebb559d224c0b6b6d01afce87c51ba"
uuid = "35ca27e7-8b34-5b7f-bca9-bdc33f59eb06"
version = "3.2.9+0"

[[deps.fzf_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "6e50f145003024df4f5cb96c7fce79466741d601"
uuid = "214eeab7-80f7-51ab-84ad-2988db7cef09"
version = "0.56.3+0"

[[deps.gperf_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg"]
git-tree-sha1 = "0ba42241cb6809f1a278d0bcb976e0483c3f1f2d"
uuid = "1a1c6b14-54f6-533d-8383-74cd7377aa70"
version = "3.1.1+1"

[[deps.libaom_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "522c1df09d05a71785765d19c9524661234738e9"
uuid = "a4ae2306-e953-59d6-aa16-d00cac43593b"
version = "3.11.0+0"

[[deps.libass_jll]]
deps = ["Artifacts", "Bzip2_jll", "FreeType2_jll", "FriBidi_jll", "HarfBuzz_jll", "JLLWrappers", "Libdl", "Zlib_jll"]
git-tree-sha1 = "e17c115d55c5fbb7e52ebedb427a0dca79d4484e"
uuid = "0ac62f75-1d6f-5e53-bd7c-93b484bb37c0"
version = "0.15.2+0"

[[deps.libblastrampoline_jll]]
deps = ["Artifacts", "Libdl"]
uuid = "8e850b90-86db-534c-a0d3-1478176c7d93"
version = "5.11.0+0"

[[deps.libdecor_jll]]
deps = ["Artifacts", "Dbus_jll", "JLLWrappers", "Libdl", "Libglvnd_jll", "Pango_jll", "Wayland_jll", "xkbcommon_jll"]
git-tree-sha1 = "9bf7903af251d2050b467f76bdbe57ce541f7f4f"
uuid = "1183f4f0-6f2a-5f1a-908b-139f9cdfea6f"
version = "0.2.2+0"

[[deps.libevdev_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg"]
git-tree-sha1 = "141fe65dc3efabb0b1d5ba74e91f6ad26f84cc22"
uuid = "2db6ffa8-e38f-5e21-84af-90c45d0032cc"
version = "1.11.0+0"

[[deps.libfdk_aac_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "8a22cf860a7d27e4f3498a0fe0811a7957badb38"
uuid = "f638f0a6-7fb0-5443-88ba-1cc74229b280"
version = "2.0.3+0"

[[deps.libinput_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg", "eudev_jll", "libevdev_jll", "mtdev_jll"]
git-tree-sha1 = "ad50e5b90f222cfe78aa3d5183a20a12de1322ce"
uuid = "36db933b-70db-51c0-b978-0f229ee0e533"
version = "1.18.0+0"

[[deps.libpng_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Zlib_jll"]
git-tree-sha1 = "068dfe202b0a05b8332f1e8e6b4080684b9c7700"
uuid = "b53b4c65-9356-5827-b1ea-8c7a1a84506f"
version = "1.6.47+0"

[[deps.libvorbis_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Ogg_jll", "Pkg"]
git-tree-sha1 = "490376214c4721cdaca654041f635213c6165cb3"
uuid = "f27f6e37-5d2b-51aa-960f-b287f2bc3b7a"
version = "1.3.7+2"

[[deps.mtdev_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg"]
git-tree-sha1 = "814e154bdb7be91d78b6802843f76b6ece642f11"
uuid = "009596ad-96f7-51b1-9f1b-5ce2d5e8a71e"
version = "1.1.6+0"

[[deps.nghttp2_jll]]
deps = ["Artifacts", "Libdl"]
uuid = "8e850ede-7688-5339-a07c-302acd2aaf8d"
version = "1.59.0+0"

[[deps.oneTBB_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl"]
git-tree-sha1 = "d5a767a3bb77135a99e433afe0eb14cd7f6914c3"
uuid = "1317d2d5-d96f-522e-a858-c73665f53c3e"
version = "2022.0.0+0"

[[deps.p7zip_jll]]
deps = ["Artifacts", "Libdl"]
uuid = "3f19e933-33d8-53b3-aaab-bd5110c3b7a0"
version = "17.4.0+2"

[[deps.x264_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg"]
git-tree-sha1 = "4fea590b89e6ec504593146bf8b988b2c00922b2"
uuid = "1270edf5-f2f9-52d2-97e9-ab00b5d0237a"
version = "2021.5.5+0"

[[deps.x265_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg"]
git-tree-sha1 = "ee567a171cce03570d77ad3a43e90218e38937a9"
uuid = "dfaa095f-4041-5dcd-9319-2fabd8486b76"
version = "3.5.0+0"

[[deps.xkbcommon_jll]]
deps = ["Artifacts", "JLLWrappers", "Libdl", "Pkg", "Wayland_jll", "Wayland_protocols_jll", "Xorg_libxcb_jll", "Xorg_xkeyboard_config_jll"]
git-tree-sha1 = "63406453ed9b33a0df95d570816d5366c92b7809"
uuid = "d8fb68d0-12a3-5cfd-a85a-d49703b185fd"
version = "1.4.1+2"
"""

# ╔═╡ Cell order:
# ╠═d871dcc0-fe0f-11ef-1d52-277e2f226a7a
# ╠═d1634666-de2d-4746-b0aa-1dd8b94193a9
# ╠═6d686588-4878-4ad6-a88b-d2fa155f9260
# ╠═e9e3bd04-3446-4db2-a6a9-9c557b120c90
# ╠═110c2e45-3000-4c32-acdf-75f0c703e2e3
# ╠═5e9cb45f-5cfe-4139-8ca4-7b8a2d7ec8a3
# ╠═4662b0f9-f265-4d36-a42b-52a4e3006e27
# ╠═86d63da4-aa17-49ff-8bdb-57b1a42265c6
# ╠═80033eaa-4e5a-47e7-914b-9a4c5c3e2d35
# ╠═f271e3db-625c-4d9e-9178-317b16b9de7d
# ╠═9286d50a-7d0b-479a-8ed2-46d9278e78c9
# ╠═f0b331af-5991-4e18-9b42-22cca57e2372
# ╠═fd43e78e-38fa-4856-88e6-2ad8d8283cb1
# ╠═399e6058-ce84-4f62-8d8c-5a63447dc531
# ╠═c4a871c2-f5c9-417a-9b9a-861957e290a0
# ╠═4ade4b71-d338-42c0-bc2b-5e4a5a3559d8
# ╠═4c3192b1-92f7-4b44-bc37-7d995898db38
# ╟─70e2c383-47da-4e87-8df5-8155a06ffec9
# ╠═24c4cb9f-fad3-4906-8d4e-870ac1248ccf
# ╠═57c068ef-9140-4a6f-9cbc-1b5db5a0da0b
# ╟─6a87a303-ea11-4ade-8c62-0610ba308133
# ╠═8f99183f-a942-49dd-a777-bb753ed0ef86
# ╠═44b7d81a-3af9-418e-bcc4-ec1b293ce1ff
# ╠═3926079b-c192-4d8b-9979-23596d4218d6
# ╠═c55746f6-1443-4ab9-86db-457c2f1536ca
# ╠═423ad10d-62ab-41e1-8cf5-98e64e4be3b3
# ╠═607de4af-7c69-465b-98c3-98afced5dd3c
# ╠═06d5b96f-a539-4e2d-b704-5dc9eeb6cef1
# ╠═0ee33fef-7c8d-4d19-b4f4-56a40533fba6
# ╠═8302985e-8574-407b-a7c0-6cfeeb36a49b
# ╠═6bdafcd6-f48b-40aa-bac6-9c6dfef6ab58
# ╠═32745471-ef11-4f79-9539-fbc74b3a4c72
# ╠═94a1c7e3-99c9-4ea6-a987-7d97a8de1501
# ╠═2cfcff2d-0e93-482d-9c6b-2b6b853f0a3d
# ╠═3a063e60-6a6e-45d6-b259-a06590e03bc4
# ╠═eca816c2-1003-4a51-af73-5dac1966e783
# ╠═e474d169-21ac-4e84-b502-160428120014
# ╠═3e8d13ab-f3c7-4137-9921-87cf0cdbdb34
# ╠═d64aa272-60b0-4bf1-bf33-dc7348c02581
# ╠═273aa79f-cae0-42fa-82bf-bfbae74d436b
# ╠═ba5bd2f6-3139-49c0-939a-340e0a8a7a28
# ╠═d5699b4f-ca9d-4bba-8173-bbdf5ed1ca0c
# ╠═6d3c1881-81ab-4d37-82fc-3a2375919cc3
# ╠═90bf8f8c-046f-4999-a9ac-8b6ce2c41cd8
# ╠═830eee87-a422-436c-b8c9-8f1677974817
# ╠═ac277bf7-a042-4ab4-ba8a-d55e3b038dab
# ╠═cd13189e-3626-468d-b484-4b01b6266ecb
# ╠═aace243e-bd5f-4aaa-b5f2-4e6d4002a163
# ╠═de3182c9-1d7c-4937-8f2c-07bce0070ec9
# ╠═d74023f8-4098-4371-9055-39bc047d0478
# ╠═340f88d7-2abd-4dd3-a782-30a80c6414e9
# ╠═66ffc948-8040-44e4-8d97-57a31dd44e70
# ╠═e3a4f8b5-be50-495d-801b-08bd0e2bd5cf
# ╟─46773db9-d951-45c5-9588-c7aafcbe1bae
# ╟─b0248751-5f3c-418d-8d41-10ae41fdccb4
# ╟─46eec85f-9e59-47de-831e-f6944d4ca377
# ╟─98dd1055-2574-4d9d-8c40-0b7531c339c3
# ╟─1033d92b-c35b-4231-b8d6-17ba89b10ad5
# ╟─88a69b5d-60df-45ab-9f6a-2e2ee7eac989
# ╠═28151031-5782-4582-98c7-afe7f24d41d4
# ╟─01425fc1-fc28-40c4-872c-ffcef57764f6
# ╠═6eb799dd-0bf1-4cb0-b5fe-d6ddcad0ee8f
# ╠═4bcd7bd6-f4f1-46d5-8ecf-435941bcbcd4
# ╠═f4e79b84-a224-4040-b6d2-a93f231ba557
# ╠═d1dc9c7c-5019-442c-8128-c63f111aba4d
# ╟─b84a1cc8-3cc2-4eac-910a-79f3f56edbd4
# ╠═b7f6fd98-5593-43bc-ac0e-b6ed5978a524
# ╠═207ecbca-2bc5-445e-b603-f3c717e83e45
# ╠═1ee4d26b-042c-411b-8d25-0ea22dbac330
# ╟─00000000-0000-0000-0000-000000000001
# ╟─00000000-0000-0000-0000-000000000002
