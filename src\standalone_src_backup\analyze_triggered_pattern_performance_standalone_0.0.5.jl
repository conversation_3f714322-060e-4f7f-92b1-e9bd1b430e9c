# include("src/analyze_triggered_pattern_performance_standalone.jl")

# --- 套件載入 ---
using Combinatorics, CSV, DataFrames, ThreadSafeDicts
using StaticArrays
using Printf
using StatsBase, Dates, Serialization


# --- 全域設定 ---
global CONFIG = (
    game_name = "fan5",
    data_file_path = "data/fan5.csv",
    max_number = 39,
    gx_size = 2,
    numbers_in_draw = 5,
    
    # 數據分析範圍
    analysis_start_period = 1,
    analysis_end_period = 1000,
    
    # 模式回溯期數
    gx_wins_lookback = 4,
    pattern_lookback = 4,
    
    # 高表現模式過濾條件
    filter_min_hit_rate = 0.14,
    filter_max_hit_rate = nothing,
    filter_min_instances = 2,
    filter_max_instances = nothing, # nothing 表示無上限

    # 查找 Gx 組合的目標期號
    target_period_to_find_gxs = 1001,

    # 結果儲存設定
    save_results = true,
    results_dir = "results"
)

# --- 模組定義 ---

"""
模組：DataTypeModule
功能：定義此分析腳本中使用的核心資料結構。
"""
module DataTypeModule
using StaticArrays
export GxPrice, TriggeredPatternStats

struct GxPrice
    cum7::Vector{Int8}
    cum14::Vector{Int8}
    cum21::Vector{Int8}
    gx_wins::Vector{Int8}
    gx::Vector{Int8} 
end

mutable struct TriggeredPatternStats{K_GXWINS, L_CUM}
    gx_wins_pattern::SVector{K_GXWINS, Int8} 
    cum7_pattern::SVector{L_CUM, Int8} 
    cum14_pattern::SVector{L_CUM, Int8}
    cum21_pattern::SVector{L_CUM, Int8}
    total_future_wins::Int
    num_trigger_instances::Int
    hit_rate::Float64
end

end # --- 結束 DataTypeModule ---


"""
模組：DataHandlingModule
功能：處理數據的載入、預處理和計算。
"""
module DataHandlingModule
using ..DataTypeModule
using CSV, DataFrames, StaticArrays, ThreadSafeDicts

export load_lottery_data, calculate_gxs_gxprice_parallel

function count_wins(data::Vector{<:SVector}, gx::SVector)
    counts = zeros(Int8, length(data))
    for i in eachindex(data)
        counts[i] = Int8(length(intersect(data[i], gx)))
    end
    return counts
end

function smsint16(wins::Vector{Int8}, window::Int)
    n = length(wins)
    result = zeros(Int8, n)
    for i in window:n
        result[i] = sum(view(wins, i-window+1:i))
    end
    return result
end

function create_car_3crazys_price(data::Vector{SVector{N, Int8}}, gx::SVector{M, Int8}) where {N, M}
    gx_wins = count_wins(data, gx)
    cum7 = smsint16(gx_wins, 7) .- Int8(M * 1) 
    cum14 = smsint16(gx_wins, 14) .- Int8(M * 2)
    cum21 = smsint16(gx_wins, 21) .- Int8(M * 3)
    return GxPrice(cum7, cum14, cum21, gx_wins, gx)
end

function calculate_gxs_gxprice_parallel(numbers::Vector{SVector{N, Int8}}, gxs::Vector{SVector{M, Int8}}) where {N, M}
    gxsprices = ThreadSafeDict{SVector{M, Int8}, GxPrice}()
    Threads.@threads for combo in gxs
        gxsprices[combo] = create_car_3crazys_price(numbers, combo)
    end
    return Dict(gxsprices) 
end

function load_lottery_data(file_path::String, N::Int)::Vector{SVector{N, Int8}}
    isfile(file_path) || throw(ArgumentError("檔案 $file_path 不存在"))
    @info "從 $(file_path) 加載數據"
    df = CSV.File(file_path, header=false) |> DataFrame
    numbers = [SVector{N, Int8}(Tuple(row[2:end])) for row in eachrow(df)]
    @info "數據加載成功。總記錄數: $(nrow(df))"
    return numbers
end

end # --- 結束 DataHandlingModule ---


"""
模組：AnalysisModule
功能：執行模式分析和統計計算。
"""
module AnalysisModule

using ..DataTypeModule
using StaticArrays

export analyze_triggered_pattern_performance, filter_and_sort_triggered_stats, find_stats_for_pattern

function get_future_wins_for_gx(gx_wins_vector::Vector{Int8}, trigger_idx::Int, n_future::Int)::Vector{Int8}
    len_gx_wins = length(gx_wins_vector)
    end_idx = trigger_idx + n_future
    end_idx > len_gx_wins && return gx_wins_vector[trigger_idx+1:end]
    return gx_wins_vector[trigger_idx+1:end_idx]
end

function _extract_patterns_at_index(price_data::GxPrice, idx::Int, ::Val{K}, ::Val{L}) where {K, L}
    gx_wins_pattern = SVector{K, Int8}(view(price_data.gx_wins, idx-K+1:idx))
    cum7_pattern = SVector{L, Int8}(view(price_data.cum7, idx-L+1:idx))
    cum14_pattern = SVector{L, Int8}(view(price_data.cum14, idx-L+1:idx))
    cum21_pattern = SVector{L, Int8}(view(price_data.cum21, idx-L+1:idx))
    return (gx_wins_pattern, cum7_pattern, cum14_pattern, cum21_pattern)
end

function _accumulate_pattern_stats(gxsprices::Dict{SVector{M, Int8}, GxPrice}, start_idx::Int, end_idx::Int, n_future::Int, ::Val{K}, ::Val{L}) where {M, K, L}
    PatternKeyType = Tuple{SVector{K, Int8}, SVector{L, Int8}, SVector{L, Int8}, SVector{L, Int8}}
    accumulators = Dict{PatternKeyType, NamedTuple{(:sum_future_wins, :count), Tuple{Ref{Int}, Ref{Int}}}}()
    min_hist = max(K, L)

    for price_data in values(gxsprices)
        loop_end_idx = min(length(price_data.gx_wins) - n_future, end_idx)
        for i in min_hist:loop_end_idx
            if i >= start_idx
                pattern_key = _extract_patterns_at_index(price_data, i, Val(K), Val(L))
                future_wins = get_future_wins_for_gx(price_data.gx_wins, i, n_future)
                
                acc = get!(() -> (sum_future_wins=Ref(0), count=Ref(0)), accumulators, pattern_key)
                acc.sum_future_wins[] += sum(future_wins)
                acc.count[] += 1
            end
        end
    end
    return accumulators
end

function _create_stats_from_accumulators(accumulators, gx_size, n_future, ::Val{K}, ::Val{L}) where {K, L}
    stats = TriggeredPatternStats{K, L}[]
    sizehint!(stats, length(accumulators))
    for (key, acc) in accumulators
        total_wins = acc.sum_future_wins[]
        num_instances = acc.count[]
        denominator = gx_size * n_future * num_instances
        hit_rate = (denominator > 0) ? (Float64(total_wins) / denominator) : 0.0
        push!(stats, TriggeredPatternStats{K, L}(key..., total_wins, num_instances, hit_rate))
    end
    return stats
end

function analyze_triggered_pattern_performance(gxsprices, start_idx, end_idx, n_future, gx_size, vK::Val{K}, vL::Val{L}) where {K, L}
    accumulators = _accumulate_pattern_stats(gxsprices, start_idx, end_idx, n_future, vK, vL)
    stats = _create_stats_from_accumulators(accumulators, gx_size, n_future, vK, vL)
    sort!(stats, by = x -> x.hit_rate, rev=true)
    return stats
end

function filter_and_sort_triggered_stats(stats; sort_by=:hit_rate, sort_rev=true, min_hit_rate=nothing, max_hit_rate=nothing, min_num_instances=nothing, max_num_instances=nothing, gx_wins_pattern=nothing)
    filtered = copy(stats)
    !isnothing(min_hit_rate) && filter!(s -> s.hit_rate >= min_hit_rate, filtered)
    !isnothing(max_hit_rate) && filter!(s -> s.hit_rate <= max_hit_rate, filtered)
    !isnothing(min_num_instances) && filter!(s -> s.num_trigger_instances >= min_num_instances, filtered)
    !isnothing(max_num_instances) && filter!(s -> s.num_trigger_instances <= max_num_instances, filtered)
    !isnothing(gx_wins_pattern) && filter!(s -> s.gx_wins_pattern == gx_wins_pattern, filtered)
    sort!(filtered, by = s -> getproperty(s, sort_by), rev = sort_rev)
    return filtered
end

function find_stats_for_pattern(pattern_vectors, stats_results)
    length(pattern_vectors) != 4 && return nothing
    K, L = length(pattern_vectors[1]), length(pattern_vectors[2])
    target_key = (SVector{K, Int8}(pattern_vectors[1]), SVector{L, Int8}(pattern_vectors[2]), SVector{L, Int8}(pattern_vectors[3]), SVector{L, Int8}(pattern_vectors[4]))
    
    for stat in stats_results
        stat_key = (stat.gx_wins_pattern, stat.cum7_pattern, stat.cum14_pattern, stat.cum21_pattern)
        if stat_key == target_key
            return stat
        end
    end
    return nothing
end

end # --- 結束 AnalysisModule ---


"""
模組：PredictionModule
功能：根據分析結果，查找並預測符合條件的 Gx 組合。
"""
module PredictionModule

using ..DataTypeModule, ..AnalysisModule
using StaticArrays

export find_gxs_for_high_performing_patterns_at_period, find_gxs_for_pattern_info_collected_at_period

function find_gxs_for_high_performing_patterns_at_period(gxsprices::Dict{SVector{M, Int8}, GxPrice}, high_performing_stats::Vector{<:TriggeredPatternStats{K, L}}, target_period::Int, start_period::Int) where {M, K, L}
    @info "為期號 $(target_period) 查找匹配高表現模式的 Gx 組合..."
    PatternKeyType = Tuple{SVector{K, Int8}, SVector{L, Int8}, SVector{L, Int8}, SVector{L, Int8}}
    found_gxs_map = Dict{PatternKeyType, Vector{SVector{M, Int8}}}()
    
    target_keys = Set( (s.gx_wins_pattern, s.cum7_pattern, s.cum14_pattern, s.cum21_pattern) for s in high_performing_stats)
    @info "準備查找 $(length(target_keys)) 個獨特的高表現模式。"
    isempty(target_keys) && return found_gxs_map

    idx_in_data = target_period - start_period + 1
    min_hist = max(K, L)
    idx_in_data < min_hist && (@warn "目標期號 $target_period 過早，無法查找。"; return found_gxs_map)

    for (gx, price_data) in gxsprices
        idx_in_data > length(price_data.gx_wins) && continue
        
        observed_key = AnalysisModule._extract_patterns_at_index(price_data, idx_in_data, Val(K), Val(L))
        if observed_key in target_keys
            push!(get!(() -> Vector{SVector{M, Int8}}(), found_gxs_map, observed_key), gx)
        end
    end
    @info "查找完成。共找到 $(length(found_gxs_map)) 個模式匹配，對應總共 $(sum(length, values(found_gxs_map); init=0)) 個 Gx 組合。"
    return found_gxs_map
end

function find_gxs_for_pattern_info_collected_at_period(gxsprices::Dict{SVector{M, Int8}, GxPrice}, pattern_info, target_period::Int, start_period::Int, ::Val{K}, ::Val{L}) where {M, K, L}
    @info "為期號 $(target_period) 從手動收集的模式中查找匹配的 Gx 組合..."
    target_stats = [TriggeredPatternStats{K,L}(SVector{K,Int8}(p[1][1]), SVector{L,Int8}(p[1][2]), SVector{L,Int8}(p[1][3]), SVector{L,Int8}(p[1][4]), 0, 0, 0.0) for p in pattern_info]
    return find_gxs_for_high_performing_patterns_at_period(gxsprices, target_stats, target_period, start_period)
end

end # --- 結束 PredictionModule ---

"""
模組：LoggingModule
功能：將評估結果記錄到 Markdown 文件中。
"""
module LoggingModule

using Dates
using Printf

export log_evaluation_to_markdown

function get_next_record_number(log_file_path::String)
    !isfile(log_file_path) && return 1
    
    content = read(log_file_path, String)
    matches = eachmatch(r"### 紀錄 #(\d+)", content)
    
    isempty(matches) && return 1
    
    last_match = collect(matches)[end]
    last_number = parse(Int, last_match.captures[1])
    
    return last_number + 1
end

function format_config_for_markdown(config)
    lines = ["{"]
    for (key, value) in pairs(config)
        val_str = if isa(value, String)
            "\"$(value)\""
        elseif isnothing(value)
            "null"
        else
            string(value)
        end
        push!(lines, "    \"$(key)\": $(val_str),")
    end
    if length(lines) > 1
        lines[end] = rstrip(lines[end], ',')
    end
    push!(lines, "}")
    return join(lines, "\n")
end

function generate_remarks(config, eval_results)
    profit_ratio = eval_results.profit_ratio
    num_invest = eval_results.num_invest
    
    # --- 結果 ---
    result_text = if profit_ratio > 0.5
        "本次參數組合表現極佳，產生了非常可觀的利潤。"
    elseif profit_ratio > 0.1
        "本次參數組合表現良好，產生了穩定的正利潤。"
    elseif profit_ratio > 0.0
        "本次參數組合產生了微幅利潤，策略基本有效。"
    elseif profit_ratio > -0.1
        "本次參數組合結果接近損益兩平，產生了微幅虧損。"
    else
        "本次參數組合產生了顯著虧損，策略需要重新評估。"
    end

    # --- 分析 ---
    analysis_text = "篩選條件為 `hit_rate` 區間: [$(config.filter_min_hit_rate), $(config.filter_max_hit_rate)]，`min_instances`: $(config.filter_min_instances)。"
    if num_invest > 150
        analysis_text *= " 最終產生了 $(num_invest) 個投資組合，數量偏多，可能表示篩選條件相對寬鬆，導致納入了統計上不夠穩健的模式，增加了總成本。"
    elseif num_invest < 20
        analysis_text *= " 最終產生了 $(num_invest) 個投資組合，數量較少，表示篩選條件相對嚴格，策略較為保守。"
    else
        analysis_text *= " 最終產生了 $(num_invest) 個投資組合，數量適中。"
    end

    # --- 未來應用建議 ---
    suggestion_text = if profit_ratio > 0.1
        "此參數組合在本次評估中表現強勁，具有應用於未來期號的潛力。建議持續監控其表現的穩定性。"
    elseif profit_ratio > 0.0
        "此參數組合顯示出一定的盈利能力。若要應用於未來，可考慮微調參數以尋求更高的回報，或維持現有設定以進行穩健觀察。"
    else
        "若要將此參數集應用於其他期號，建議優先考慮收緊篩選條件，例如提高 `filter_min_instances` (例如，調整至 5 或更高) 或進一步縮小 `hit_rate` 區間，以篩選出更少但更精準的 Gx 組合，從而控制投資風險。"
    end

    return """- **結果:** $(result_text)
- **分析:** $(analysis_text)
- **未來應用建議:** $(suggestion_text)"""
end

function log_evaluation_to_markdown(log_file_path::String, config, eval_results)
    record_number = get_next_record_number(log_file_path)
    
    open(log_file_path, "a") do io
        write(io, "\n---\n\n")
        write(io, "### 紀錄 #$(record_number)\n\n")
        
        write(io, "- **紀錄時間:** $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))\n")
        write(io, "- **查找 Gx 組合的目標期號:** `$(config.target_period_to_find_gxs)`\n")
        write(io, "- **評估對象期號:** `$(config.target_period_to_find_gxs + 1)`\n")
        write(io, "- **評估對象開獎號碼:** `$(eval_results.next_draw)`\n\n")
        
        write(io, "#### 使用參數 (`CONFIG`)\n\n")
        write(io, "```json\n")
        write(io, format_config_for_markdown(config))
        write(io, "\n```\n\n")
        
        write(io, "#### 投資效益評估結果\n\n")
        write(io, "- **投資組合數量:** $(eval_results.num_invest)\n")
        write(io, "- **總成本:** $(eval_results.cost)\n")
        write(io, "- **總回報:** $(eval_results.revenue)\n")
        write(io, "- **總利潤:** $(eval_results.profit)\n")
        write(io, @Printf.sprintf("- **利潤率:** %.2f%%\n\n", eval_results.profit_ratio * 100))
        
        write(io, "#### 結論與備註\n\n")
        remarks = generate_remarks(config, eval_results)
        write(io, remarks * "\n\n")
    end
    
    @info "評估結果已記錄至: $(log_file_path)"
end

end # --- 結束 LoggingModule ---

# --- 主執行流程 ---

# 匯入所有模組的函式
using .DataTypeModule, .DataHandlingModule, .AnalysisModule, .PredictionModule, .LoggingModule

# 載入外部定義的手動模式
include("manual_patterns.jl")

function perform_historical_analysis(config)
    # --- 1. 資料準備 ---
    @info "--- 階段 1: 資料準備 ---"
    gxs_combinations = collect(combinations(1:CONFIG.max_number, CONFIG.gx_size)) |> (x -> map(SVector{CONFIG.gx_size, Int8}, x))
    all_numbers_data = load_lottery_data(CONFIG.data_file_path, CONFIG.numbers_in_draw)
    gxsprices_data = calculate_gxs_gxprice_parallel(all_numbers_data, gxs_combinations)

    # --- 2. 歷史表現分析 ---
    @info "--- 階段 2: 歷史表現分析 ---"
    K, L = Val(CONFIG.gx_wins_lookback), Val(CONFIG.pattern_lookback)
    stats_1day = analyze_triggered_pattern_performance(gxsprices_data, CONFIG.analysis_start_period, CONFIG.analysis_end_period, 1, CONFIG.gx_size, K, L)
    
    # --- 3. 篩選高表現模式 ---
    @info "--- 階段 3: 篩選高表現模式 ---"
    high_perf_stats_1day = filter_and_sort_triggered_stats(stats_1day, 
        min_hit_rate=config.filter_min_hit_rate, max_hit_rate=config.filter_max_hit_rate,
        min_num_instances=config.filter_min_instances, max_num_instances=config.filter_max_instances)

    return (
        all_numbers_data = all_numbers_data,
        gxsprices_data = gxsprices_data,
        high_perf_stats = high_perf_stats_1day,
        all_stats = stats_1day
    )
end

function evaluate_for_target_period(analysis_results, target_period, config)
    @info "\n===== 評估目標期號: $(target_period) ====="
    
    # --- 4. 根據模式查找當期 Gx ---
    @info "--- 階段 4: 查找當期 Gx 組合 ---"
    gxsprices_data = analysis_results.gxsprices_data
    high_perf_stats_1day = analysis_results.high_perf_stats
    all_numbers_data = analysis_results.all_numbers_data

    # 基於統計篩選的模式
    matched_gxs_auto = find_gxs_for_high_performing_patterns_at_period(gxsprices_data, high_perf_stats_1day, target_period, CONFIG.analysis_start_period)
    all_matched_gxs = vcat(collect(values(matched_gxs_auto))...)
    
    @info "在期號 $(target_period) 總共找到 $(length(all_matched_gxs)) 個匹配高表現模式的 Gx 組合。"
    println("匹配的 Gx 組合列表:")
    foreach(println, all_matched_gxs)

    # --- 5. 結果儲存與評估 ---
    @info "--- 階段 5: 結果儲存與評估 ---"
    if config.save_results
        !isdir(config.results_dir) && mkdir(config.results_dir)
        hit_rate_str = "min_$(config.filter_min_hit_rate)_max_$(config.filter_max_hit_rate)"
        instances_str = "min_$(config.filter_min_instances)_max_$(config.filter_max_instances)"
        filename = joinpath(config.results_dir, "$(config.game_name)_gxs_p$(target_period)_hr_$(hit_rate_str)_inst_$(instances_str).jls")
        serialize(filename, all_matched_gxs)
        @info "結果已儲存至: $(filename)"
    end

    if length(all_matched_gxs) !== 0
        if target_period < length(all_numbers_data)
            next_draw = all_numbers_data[target_period + 1]
            wins = intersect.(all_matched_gxs, [next_draw])
            num_invest = length(all_matched_gxs)
            cost = round(num_invest * config.gx_size * 30.4, digits=2)
            revenue = round(sum(length.(wins); init=0) * 212, digits=2)
            profit = round(revenue - cost, digits=2)
            profit_ratio = cost > 0 ? round(profit / cost, digits=4) : 0.0
            
            println("\n--- 投資效益評估 (對比下一期: $(next_draw)) ---")
            println("投資組合數量: $(num_invest)")
            println("總成本: $(cost)")
            println("總回報: $(revenue)")
            println("總利潤: $(profit)")
            println("利潤率: $(profit_ratio)")
            
            # --- 新增：自動記錄日誌 ---
            if config.save_results # 重複使用此旗標來控制日誌記錄
                log_file_path = joinpath(dirname(@__DIR__), "投資效益評估與參數紀錄.md")
                
                # 建立一個臨時的 config 來記錄，包含當前的 target_period
                current_config = merge(NamedTuple(config), (target_period_to_find_gxs = target_period,))

                eval_results = (
                    next_draw = next_draw,
                    num_invest = num_invest,
                    cost = cost,
                    revenue = revenue,
                    profit = profit,
                    profit_ratio = profit_ratio
                )
                log_evaluation_to_markdown(log_file_path, current_config, eval_results)
            end
        end
    end
    return all_matched_gxs
end

function main0()
    # --- 執行單次評估 ---
    @info "執行單次歷史分析..."
    analysis_results = perform_historical_analysis(CONFIG)
    
    @info "對單一目標期號進行評估..."
    evaluate_for_target_period(analysis_results, CONFIG.target_period_to_find_gxs, CONFIG)

    # --- 如何對多個目標期數進行評估 ---
    # @info "\n\n===== 開始對多個目標期數進行批量評估 ====="
    # target_periods_to_evaluate = [1015, 1016, 1017, 1018] # 您想評估的期數列表
    # for period in target_periods_to_evaluate
    #     evaluate_for_target_period(analysis_results, period, CONFIG)
    # end
end

# --- 執行主函式 ---
# result = main0()

function main()
    # --- 執行單次評估 ---
    @info "執行單次歷史分析..."
    # 1. 執行一次耗時的分析，並將結果儲存起來
    analysis_results = perform_historical_analysis(CONFIG)
    
    # @info "對單一目標期號進行評估..."
    # evaluate_for_target_period(analysis_results, CONFIG.target_period_to_find_gxs, CONFIG)

    # --- 如何對多個目標期數進行評估 ---
    @info "\n\n===== 開始對多個目標期數進行批量評估 ====="
    # 2. 定義您想評估的所有目標期號
    target_periods_to_evaluate = collect(1176:1178) # [1015, 1016, 1017, 1018] # 您想評估的期數列表
    
    # 3. 迴圈調用評估函式，這一步會非常快
    for period in target_periods_to_evaluate
        evaluate_for_target_period(analysis_results, period, CONFIG)
    end
end

result = main()

# to-do list
# - [x] 如何修改 `analyze_triggered_pattern_performance_standalone.jl` 腳本，讓它在每次評估後自動將結果寫入這個日誌文件？
# - [x] 如何修改日誌記錄功能，讓「結論與備註」可以根據利潤率自動生成初步的文字？
# - [ ] 增加一個功能，將多個最佳參數組合生成的 Gx 建議進行合併和去重，並根據其在不同最佳參數集中的出現頻率或加權得分進行排序，提供一個最終的、精簡的投資列表。