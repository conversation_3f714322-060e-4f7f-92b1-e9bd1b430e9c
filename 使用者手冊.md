# 彩票模式分析與預測工具 - 使用者手冊

## 目錄
1. [概述](#概述)
2. [系統需求](#系統需求)
3. [安裝與設置](#安裝與設置)
4. [快速開始](#快速開始)
5. [功能詳解](#功能詳解)
6. [配置參數](#配置參數)
7. [使用情境範例](#使用情境範例)
8. [輸出文件說明](#輸出文件說明)
9. [故障排除](#故障排除)
10. [進階使用](#進階使用)

## 概述

彩票模式分析與預測工具是一個基於 Julia 語言開發的高性能彩票數據分析系統。該工具採用流式處理架構，能夠：

- 分析彩票號碼的歷史模式
- 預測未來可能的中獎號碼
- 評估不同投注策略的表現
- 生成詳細的分析報告

### 核心特色
- **高性能並行計算**：利用 Julia 的多線程能力
- **靈活的模式分析**：支持自定義模式長度和參數
- **多種分析功能**：7 種不同的分析模式
- **詳細的數據輸出**：CSV 格式的分析結果
- **實時進度顯示**：清晰的處理狀態信息

## 系統需求

### 軟體需求
- Julia 1.6 或更高版本
- 必要的 Julia 套件：
  - CSV.jl
  - DataFrames.jl
  - StaticArrays.jl
  - Combinatorics.jl
  - Dates.jl
  - Printf.jl

### 硬體需求
- 記憶體：建議 8GB 以上
- 處理器：支持多核心處理器
- 儲存空間：至少 1GB 可用空間

## 安裝與設置

### 1. 安裝 Julia
```bash
# 下載並安裝 Julia
# 訪問 https://julialang.org/downloads/
```

### 2. 安裝必要套件
```julia
using Pkg
Pkg.add(["CSV", "DataFrames", "StaticArrays", "Combinatorics", "Dates", "Printf"])
```

### 3. 準備數據文件
將彩票數據文件放置在 `data/fan5.csv`，格式為：
```
日期,號碼1,號碼2,號碼3,號碼4,號碼5
20240101,01,15,23,31,39
20240102,03,12,18,25,37
...
```

### 4. 創建目錄結構
```
StandAlone/
├── src/
│   └── analyze_triggered_pattern_performance_standalone_流式處理架構.jl
├── data/
│   └── fan5.csv
└── results/
```

## 快速開始

### 基本運行
```bash
cd StandAlone
julia src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl
```

### 互動式使用
程序啟動後會顯示主菜單：
```
================================================================================
彩票模式分析與預測工具 - 流式處理架構
================================================================================
請選擇要執行的功能:
1. 查找出球率最高的模式
2. 使用特定模式進行回測
3. 分析特定模式的表現
4. 在特定期號查找匹配特定 gx_wins 模式的 Gx 組合
5. 預測下一期可能的中獎號碼
6. 分析特定期號每個 Gx 組合的模式表現並存檔
7. 取得特定期號每個 Gx 組合的模式並分析歷史表現
0. 退出
--------------------------------------------------------------------------------
請輸入選項 (0-7):
```

## 功能詳解

### 功能 1：查找出球率最高的模式
**用途**：找出歷史上表現最佳的彩票模式

**操作步驟**：
1. 選擇選項 `1`
2. 程序自動分析所有可能的模式
3. 顯示出球率最高的前 10 個模式

**輸出範例**：
```
出球率最高的模式：
1. 模式: [0, 2, 0, 2, 2, 1, 0, 0, 0] - 出球率: 34.34%
2. 模式: [1, 0, 2, 0, 1, 0, 2, 0, 2] - 出球率: 33.33%
```

### 功能 2：使用特定模式進行回測
**用途**：測試特定模式在歷史數據中的表現

**操作步驟**：
1. 選擇選項 `2`
2. 輸入模式（例如：`0,0,1,0,1,0,1,0,0`）
3. 輸入目標期號
4. 查看匹配該模式的 Gx 組合

**輸入範例**：
```
請輸入模式: 0,2,0,2,2,1,0,0,0
請輸入目標期號: 1000
```

### 功能 3：分析特定模式的表現
**用途**：深入分析特定模式的統計數據

**操作步驟**：
1. 選擇選項 `3`
2. 輸入要分析的模式
3. 查看詳細的統計結果

**輸出範例**：
```
模式 [0,2,0,2,2,1,0,0,0] 的表現分析：
總出球率: 34.34%
歷史觸發次數: 99
```

### 功能 4：查找匹配特定模式的 Gx 組合
**用途**：在特定期號找出匹配模式的所有 Gx 組合

**操作步驟**：
1. 選擇選項 `4`
2. 輸入模式和目標期號
3. 查看匹配的 Gx 組合列表

### 功能 5：預測下一期中獎號碼
**用途**：基於歷史模式預測未來可能的中獎號碼

**操作步驟**：
1. 選擇選項 `5`
2. 輸入參考模式
3. 查看預測結果和推薦號碼

**輸出範例**：
```
熱門號碼預測:
1. 號碼 1 - 出現在 80 個組合中
2. 號碼 6 - 出現在 80 個組合中

推薦的 Gx 組合:
1. [1, 2, 5]
2. [1, 2, 7]
```

### 功能 6：分析特定期號每個 Gx 組合的模式表現
**用途**：分析在特定期號每個 Gx 組合的模式表現並存檔

**操作步驟**：
1. 選擇選項 `6`
2. 輸入模式和目標期號
3. 設置過濾條件
4. 指定輸出文件名（可選）

### 功能 7：取得特定期號每個 Gx 組合的模式並分析歷史表現
**用途**：全面分析特定期號所有 Gx 組合的模式及其歷史表現

**操作步驟**：
1. 選擇選項 `7`
2. 輸入目標期號
3. 設置過濾條件
4. 查看排序後的結果並保存為 CSV

## 配置參數

### 預設配置
```julia
function get_default_config()
    return (
        data_file_path = "data/fan5.csv",
        max_number = 39,                    # 最大號碼
        numbers_in_draw = 5,                # 每期開獎號碼數
        gx_size = 3,                        # Gx 組合大小
        wins_pattern_length = 12,           # 模式長度
        n_future_draws = 2,                 # 未來期數
        results_dir = "results"             # 結果目錄
    )
end
```

### 參數說明
- `max_number`: 彩票號碼的最大值（例如：39 表示 1-39）
- `numbers_in_draw`: 每期開獎的號碼數量
- `gx_size`: Gx 組合的大小（通常為 2 或 3）
- `wins_pattern_length`: 分析模式的長度
- `n_future_draws`: 預測的未來期數
- `results_dir`: 結果文件的保存目錄

## 使用情境範例

### 情境 1：新手用戶 - 探索最佳模式

**目標**：找出歷史上表現最好的彩票模式

**操作流程**：
```bash
# 1. 啟動程序
julia src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl

# 2. 選擇功能 1
請輸入選項 (0-7): 1

# 3. 查看結果
出球率最高的模式：
1. 模式: [0, 2, 0, 2, 2, 1, 0, 0, 0] - 出球率: 34.34%
2. 模式: [1, 0, 2, 0, 1, 0, 2, 0, 2] - 出球率: 33.33%
```

**應用建議**：
- 記錄前 5 名的模式
- 在後續分析中重點關注這些高出球率模式
- 可以用功能 3 進一步分析這些模式

### 情境 2：策略分析師 - 驗證投注策略

**目標**：驗證特定模式的可靠性

**操作流程**：
```bash
# 1. 選擇功能 3 分析特定模式
請輸入選項 (0-7): 3
請輸入模式: 0,2,0,2,2,1,0,0,0

# 2. 查看詳細分析
模式 [0,2,0,2,2,1,0,0,0] 的表現分析：
總出球率: 34.34%
歷史觸發次數: 99

# 3. 使用功能 2 進行回測驗證
請輸入選項 (0-7): 2
請輸入模式: 0,2,0,2,2,1,0,0,0
請輸入目標期號: 1000
```

**分析要點**：
- 觸發次數 ≥ 50 次的模式較為可靠
- 出球率 > 25% 的模式值得關注
- 結合多個期號進行驗證

### 情境 3：投注者 - 預測下期號碼

**目標**：預測下一期可能的中獎號碼

**操作流程**：
```bash
# 1. 選擇功能 5
請輸入選項 (0-7): 5
請輸入模式: 0,0,1,0,1

# 2. 查看預測結果
熱門號碼預測:
1. 號碼 1 - 出現在 80 個組合中
2. 號碼 6 - 出現在 80 個組合中
3. 號碼 11 - 出現在 80 個組合中

推薦的 Gx 組合:
1. [1, 2, 5]
2. [1, 2, 7]
3. [1, 2, 10]
```

**投注建議**：
- 重點關注前 3 個熱門號碼
- 可以選擇推薦的 Gx 組合進行投注
- 建議分散投注降低風險

### 情境 4：研究人員 - 深度數據分析

**目標**：生成完整的分析報告

**操作流程**：
```bash
# 1. 使用功能 7 生成全面分析
請輸入選項 (0-7): 7
請輸入目標期號: 500
請輸入最小觸發次數: 5
請輸入最小出球率: 0.2
請輸入最大出球率: 1.0
請輸入輸出文件名: analysis_period500.csv

# 2. 查看生成的 CSV 文件
# 文件位置: results/analysis_period500.csv
```

**數據應用**：
- 導入 Excel 或其他分析工具
- 進行統計學分析
- 建立預測模型

### 情境 5：系統管理員 - 批量處理

**目標**：自動化分析多個期號

**Julia 腳本範例**：
```julia
# 批量分析腳本
include("src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl")

# 設置配置
config = get_default_config()

# 分析多個期號
periods = [400, 500, 600, 700, 800]

for period in periods
    println("分析期號: $period")

    # 執行功能 7
    results = run_example_7(
        period,
        config;
        min_occurrences = 5,
        min_hit_rate = 0.15,
        output_filename = "batch_analysis_period$(period).csv"
    )

    println("完成期號 $period，找到 $(results.filtered_count) 個符合條件的組合")
end
```

### 情境 6：模式比較分析

**目標**：比較不同模式的表現

**操作流程**：
```bash
# 1. 分析模式 A
請輸入選項 (0-7): 3
請輸入模式: 0,1,0,1,0

# 2. 分析模式 B
請輸入選項 (0-7): 3
請輸入模式: 1,0,1,0,1

# 3. 比較結果並記錄
```

**比較指標**：
- 出球率高低
- 觸發次數多少
- 穩定性評估

## 輸出文件說明

### CSV 文件格式

#### 功能 6 輸出格式
```csv
Rank,Gx_Combination,Pattern,Target_Period,Total_Occurrences,Total_Hits,Hit_Rate,Hit_Rate_Percent
1,"[1,2,3]","[0,1,0,1,0]",500,15,8,0.533,53.33
```

**欄位說明**：
- `Rank`: 排名
- `Gx_Combination`: Gx 組合
- `Pattern`: 模式
- `Target_Period`: 目標期號
- `Total_Occurrences`: 總觸發次數
- `Total_Hits`: 總命中次數
- `Hit_Rate`: 出球率（小數）
- `Hit_Rate_Percent`: 出球率（百分比）

#### 功能 7 輸出格式
```csv
Rank,Gx_Combination,Pattern,Target_Period,Pattern_Length,Total_Occurrences,Total_Future_Wins,Hit_Rate,Hit_Rate_Percent
1,"[31,34]","[0,0,0,0,0,0,0,0,0,1,1,0]",500,12,6,5,0.417,41.67
```

**額外欄位**：
- `Pattern_Length`: 模式長度
- `Total_Future_Wins`: 總未來中獎球數

### 文件命名規則

- **自動命名**：`功能名稱_期號_時間戳.csv`
- **範例**：`gx_pattern_historical_performance_period500_20250706_093054.csv`
- **自定義命名**：用戶指定的文件名

## 故障排除

### 常見問題

#### 1. 數據文件錯誤
**錯誤信息**：`數據文件不存在: data/fan5.csv`

**解決方案**：
```bash
# 檢查文件是否存在
ls data/fan5.csv

# 確保文件格式正確
head -5 data/fan5.csv
```

#### 2. 記憶體不足
**錯誤信息**：`OutOfMemoryError`

**解決方案**：
- 減少 `gx_size` 參數
- 降低 `wins_pattern_length`
- 增加系統記憶體

#### 3. 輸入格式錯誤
**錯誤信息**：`輸入格式不正確`

**解決方案**：
- 確保模式用逗號分隔：`0,1,0,1,0`
- 期號必須是整數
- 出球率範圍在 0.0-1.0 之間

#### 4. 期號範圍錯誤
**錯誤信息**：`無效的目標期號`

**解決方案**：
- 檢查數據文件的期號範圍
- 確保目標期號在有效範圍內
- 期號必須 ≥ 模式長度

### 性能優化建議

#### 1. 硬體優化
- 使用 SSD 硬碟提高 I/O 性能
- 增加 RAM 容量
- 使用多核心處理器

#### 2. 軟體優化
```julia
# 設置 Julia 線程數
export JULIA_NUM_THREADS=4

# 啟動時指定線程數
julia -t 4 src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl
```

#### 3. 參數調整
- 較小的 `gx_size` 可以提高速度
- 較短的 `wins_pattern_length` 減少計算量
- 適當的過濾條件減少輸出數據量

## 進階使用

### 自定義配置

**創建自定義配置文件**：
```julia
# config.jl
function get_custom_config()
    return (
        data_file_path = "data/custom_data.csv",
        max_number = 49,                    # 修改為 1-49
        numbers_in_draw = 6,                # 修改為 6 個號碼
        gx_size = 2,                        # 修改為 2 個號碼組合
        wins_pattern_length = 10,           # 修改模式長度
        n_future_draws = 3,                 # 修改未來期數
        results_dir = "custom_results"
    )
end
```

### 程式化調用

**直接調用函數**：
```julia
include("src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl")

# 使用自定義配置
config = get_custom_config()

# 直接調用分析函數
pattern = [0, 1, 0, 1, 0]
results = run_example_3(pattern, config)

# 處理結果
println("分析完成，出球率: $(results.hit_rate)")
```

### 數據預處理

**數據清理腳本**：
```julia
using CSV, DataFrames

# 讀取原始數據
raw_data = CSV.read("raw_data.csv", DataFrame)

# 數據清理
cleaned_data = raw_data[completecases(raw_data), :]

# 格式轉換
cleaned_data.date = Date.(cleaned_data.date, "yyyy-mm-dd")

# 保存清理後的數據
CSV.write("data/fan5.csv", cleaned_data)
```

### 結果後處理

**結果分析腳本**：
```julia
using CSV, DataFrames, Statistics

# 讀取分析結果
results = CSV.read("results/analysis.csv", DataFrame)

# 統計分析
mean_hit_rate = mean(results.Hit_Rate)
std_hit_rate = std(results.Hit_Rate)

println("平均出球率: $(round(mean_hit_rate, digits=4))")
println("標準差: $(round(std_hit_rate, digits=4))")

# 篩選高性能組合
high_performance = filter(row -> row.Hit_Rate > 0.3, results)
println("高性能組合數量: $(nrow(high_performance))")
```

## 技術支援

### 聯絡信息
- 技術文檔：請參考源代碼註釋
- 問題回報：請提供詳細的錯誤信息和操作步驟

### 版本更新
- 定期檢查程序更新
- 備份重要的分析結果
- 測試新版本的兼容性

---

**版本**：1.0
**最後更新**：2025-01-06
**適用程序**：analyze_triggered_pattern_performance_standalone_流式處理架構.jl
