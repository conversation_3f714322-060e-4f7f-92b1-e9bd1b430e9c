# include("src/analyze_triggered_pattern_performance_standalone.jl")
# 此腳本專注於分析所有 gx_wins 模式及其關聯 (cum7, cum14, cum21) 模式的過去表現，


# 載入必要的套件
using Combinatorics, CSV, DataFrames, ThreadSafeDicts
using StaticArrays
using Printf # 用於格式化打印
using StatsBase
using Serialization

# --- 結構定義 ---
 
"""
    GxPrice

存儲特定 Gx 組合的歷史表現數據。
"""
struct GxPrice
    cum7::Vector{Int8}
    cum14::Vector{Int8}
    cum21::Vector{Int8}
    gx_wins::Vector{Int8}
    gx::Vector{Int8} 
end


"""
    TriggeredPatternStats{K_GXWINS, L_CUM}

存儲在特定 gx_wins 模式觸發條件下，相關 (cum7, cum14, cum21) 模式的未來表現統計。
K_GXWINS 和 L_CUM 分別代表 gx_wins 和 cumX 模式的回溯期數。
"""
mutable struct TriggeredPatternStats{K_GXWINS, L_CUM}
    gx_wins_pattern::SVector{K_GXWINS, Int8} 
    cum7_pattern::SVector{L_CUM, Int8} 
    cum14_pattern::SVector{L_CUM, Int8}
    cum21_pattern::SVector{L_CUM, Int8}
    total_future_wins::Int
    num_trigger_instances::Int
    hit_rate::Float64
end

# ---


"""
    calculate_gxs_gxprice_parallel(numbers::Vector{SVector{N, Int8}}, gxs::Vector{SVector{M, Int8}}) where {N, M}

並行計算所有 Gx 組合的 GxPrice。
"""
function calculate_gxs_gxprice_parallel(numbers::Vector{SVector{N, Int8}}, gxs::Vector{SVector{M, Int8}}) where {N, M}
    gxsprices = ThreadSafeDict{SVector{M, Int8}, GxPrice}()
    Threads.@threads for combo in gxs
        price_data = create_car_3crazys_price(numbers, combo)
        gxsprices[combo] = price_data
    end
    return Dict(gxsprices) 
end

"""
    load_lottery_data(file_path::String, N::Int)::Vector{SVector{N, Int8}}

從 CSV 檔案載入數據。
"""
function load_lottery_data(file_path::String, N::Int)::Vector{SVector{N, Int8}}
    isfile(file_path) || throw(ArgumentError("檔案 $file_path 不存在"))
    @info "從 $(file_path) 加載數據"
    df = CSV.File(file_path, header=false) |> DataFrame
    numbers = [SVector{N, Int8}(Tuple(row[2:end])) for row in eachrow(df)]
    @info "數據加載成功。總記錄數: $(nrow(df))"
    return numbers
end


"""
    count_wins(data::Vector{SVector{N, Int8}}, gx::SVector{M, Int8}) where {N, M}

計算 Gx 組合在每期數據中的中獎球數。
"""
function count_wins(data::Vector{SVector{N, Int8}}, gx::SVector{M, Int8}) where {N, M}
    counts = zeros(Int8, length(data))
    for i in eachindex(data)
        count = Int8(0)
        for val in data[i]
            if val in gx
                count += Int8(1)
            end
        end
        counts[i] = count
    end
    return counts
end

"""
    smsint16(wins::Vector{Int8}, window::Int8)::Vector{Int8}

計算滑動窗口內的總和。
"""
function smsint16(wins::Vector{Int8}, window::Int8)::Vector{Int8}
    n = length(wins)
    result = zeros(Int8, n)
    for i in window:n
        slice = wins[i-window+1:i]
        result[i] = sum(slice)
    end
    return result
end

"""
    create_car_3crazys_price(data::Vector{SVector{N, Int8}}, gx::SVector{M, Int8}) where {N, M}

創建 GxPrice 結構，包含計算出的 cum7, cum14, cum21 和 gx_wins。
"""
function create_car_3crazys_price(data::Vector{SVector{N, Int8}}, gx::SVector{M, Int8}) where {N, M}
    gx_length = M
    gx_wins = count_wins(data, gx)
    cum7 = smsint16(gx_wins, Int8(7)) .- Int8(gx_length * 1) 
    cum14 = smsint16(gx_wins, Int8(14)) .- Int8(gx_length * 2)
    cum21 = smsint16(gx_wins, Int8(21)) .- Int8(gx_length * 3)
    return GxPrice(cum7, cum14, cum21, gx_wins, gx)
end


"""
    get_future_wins_for_gx(...)
取得在觸發條件後的 n 期中獎球數。
"""
function get_future_wins_for_gx(
    gx_wins_vector::Vector{Int8},
    trigger_idx_in_vector::Int,
    n_future::Int
)::Vector{Int8}
    future_wins_list = Int8[]
    len_gx_wins = length(gx_wins_vector)
    for i = 1:n_future
        next_idx = trigger_idx_in_vector + i
        if next_idx <= len_gx_wins
            push!(future_wins_list, gx_wins_vector[next_idx])
        else
            break 
        end
    end
    return future_wins_list
end

"""
    _extract_patterns_at_index(price_data, idx, K, L)

內部輔助函式：在指定索引處提取所有相關模式。
"""
function _extract_patterns_at_index(
    price_data::GxPrice, 
    idx::Int, 
    ::Val{K}, 
    ::Val{L}
) where {K, L}
    start_idx_gx_wins = idx - K + 1
    gx_wins_pattern = SVector{K, Int8}(price_data.gx_wins[start_idx_gx_wins:idx])

    start_idx_cum = idx - L + 1
    cum7_pattern = SVector{L, Int8}(price_data.cum7[start_idx_cum:idx])
    cum14_pattern = SVector{L, Int8}(price_data.cum14[start_idx_cum:idx])
    cum21_pattern = SVector{L, Int8}(price_data.cum21[start_idx_cum:idx])

    return (gx_wins_pattern, cum7_pattern, cum14_pattern, cum21_pattern)
end

"""
    _accumulate_pattern_stats(gxsprices, start_abs_idx, end_abs_idx, n_future, K, L)

內部輔助函式：遍歷數據並填充模式累加器。
"""
function _accumulate_pattern_stats(
    gxsprices::Dict{SVector{M, Int8}, GxPrice},
    data_segment_start_abs_idx::Int, 
    condition_check_end_abs_idx::Int, 
    n_future_draws::Int,
    ::Val{K_GXWINS_CONST}, 
    ::Val{L_CUM_CONST}
) where {M, K_GXWINS_CONST, L_CUM_CONST}
    
    PatternKeyType = Tuple{SVector{K_GXWINS_CONST, Int8}, SVector{L_CUM_CONST, Int8}, SVector{L_CUM_CONST, Int8}, SVector{L_CUM_CONST, Int8}}
    pattern_accumulators = Dict{PatternKeyType,
        NamedTuple{(:sum_future_wins, :count), Tuple{Ref{Int}, Ref{Int}}}
    }()
    
    min_hist_required_for_patterns = max(K_GXWINS_CONST, L_CUM_CONST)

    for price_data in values(gxsprices)
        max_relative_idx_for_check = condition_check_end_abs_idx - data_segment_start_abs_idx + 1
        loop_end_relative_idx = min(length(price_data.gx_wins), max_relative_idx_for_check)

        for idx_in_price_data in min_hist_required_for_patterns : loop_end_relative_idx
            pattern_key = _extract_patterns_at_index(price_data, idx_in_price_data, Val(K_GXWINS_CONST), Val(L_CUM_CONST))
            
            future_wins_list = get_future_wins_for_gx(price_data.gx_wins, idx_in_price_data, n_future_draws)

            if !haskey(pattern_accumulators, pattern_key)
                pattern_accumulators[pattern_key] = (sum_future_wins=Ref(0), count=Ref(0))
            end
            
            pattern_accumulators[pattern_key].sum_future_wins[] += sum(future_wins_list)
            pattern_accumulators[pattern_key].count[] += 1
        end
    end
    return pattern_accumulators
end

"""
    _create_stats_from_accumulators(accumulators, gx_size, n_future, K, L)

內部輔助函式：將累加器字典轉換為 TriggeredPatternStats 向量。
"""
function _create_stats_from_accumulators(
    pattern_accumulators::Dict,
    gx_size::Int,
    n_future_draws::Int,
    ::Val{K_GXWINS_CONST}, 
    ::Val{L_CUM_CONST}
) where {K_GXWINS_CONST, L_CUM_CONST}
    
    output_stats = TriggeredPatternStats{K_GXWINS_CONST, L_CUM_CONST}[]
    sizehint!(output_stats, length(pattern_accumulators))

    for (pattern_key, acc) in pattern_accumulators
        total_wins = acc.sum_future_wins[]
        num_instances = acc.count[]
        
        denominator = gx_size * n_future_draws * num_instances
        current_hit_rate = (denominator > 0) ? (Float64(total_wins) / denominator) : 0.0

        push!(output_stats, TriggeredPatternStats{K_GXWINS_CONST, L_CUM_CONST}(
            pattern_key[1], pattern_key[2], pattern_key[3], pattern_key[4], 
            total_wins, num_instances, current_hit_rate
        ))
    end
    return output_stats
end

"""
    analyze_triggered_pattern_performance(...)
分析所有 gx_wins 模式及其關聯模式的表現。
"""
function analyze_triggered_pattern_performance(
    gxsprices::Dict{SVector{M, Int8}, GxPrice},
    data_segment_start_abs_idx::Int, 
    condition_check_end_abs_idx::Int, 
    n_future_draws::Int,
    gx_size::Int,
    ::Val{K_GXWINS_CONST}, 
    ::Val{L_CUM_CONST}     
)::Vector{TriggeredPatternStats{K_GXWINS_CONST, L_CUM_CONST}} where {M, K_GXWINS_CONST, L_CUM_CONST}
    
    # 階段 1: 遍歷數據並填充模式累加器
    pattern_accumulators = _accumulate_pattern_stats(
        gxsprices, 
        data_segment_start_abs_idx, 
        condition_check_end_abs_idx, 
        n_future_draws, 
        Val(K_GXWINS_CONST), 
        Val(L_CUM_CONST)
    )

    # 階段 2: 將累加器字典轉換為 TriggeredPatternStats 向量
    output_stats = _create_stats_from_accumulators(
        pattern_accumulators, 
        gx_size, 
        n_future_draws, 
        Val(K_GXWINS_CONST), 
        Val(L_CUM_CONST)
    )

    # 階段 3: 排序結果並返回
    sort!(output_stats, by = x -> x.hit_rate, rev=true)
    return output_stats
end


"""
    filter_and_sort_triggered_stats(...)
根據指定條件過濾並排序 `TriggeredPatternStats` 結果。
"""
function filter_and_sort_triggered_stats(
    stats_results::Vector{TriggeredPatternStats{K,L}}; 
    sort_by::Symbol = :hit_rate, 
    sort_rev::Bool = true,       
    min_total_wins::Union{Int, Nothing} = nothing,
    min_num_instances::Union{Int, Nothing} = nothing,
    max_num_instances::Union{Int, Nothing} = nothing,
    min_hit_rate::Union{Float64, Nothing} = nothing,
    max_hit_rate::Union{Float64, Nothing} = nothing,
    gx_wins_pattern_to_filter::Union{SVector{K, Int8}, Nothing} = nothing 
)::Vector{TriggeredPatternStats{K,L}} where {K,L}
    valid_sort_fields = (:hit_rate, :total_future_wins, :num_trigger_instances)
    if !(sort_by in valid_sort_fields)
        throw(ArgumentError("無效的 sort_by 參數: $sort_by. 允許的值為: $valid_sort_fields"))
    end

    # 創建副本以避免修改原始傳入的向量
    filtered_results = copy(stats_results) 
    if !isnothing(min_total_wins)
        filter!(s -> s.total_future_wins >= min_total_wins, filtered_results)
    end
    if !isnothing(min_num_instances)
        filter!(s -> s.num_trigger_instances >= min_num_instances, filtered_results)
    end
    if !isnothing(max_num_instances)
        filter!(s -> s.num_trigger_instances <= max_num_instances, filtered_results)
    end
    if !isnothing(min_hit_rate)
        filter!(s -> s.hit_rate >= min_hit_rate, filtered_results)
    end
    if !isnothing(max_hit_rate)
        filter!(s -> s.hit_rate <= max_hit_rate, filtered_results)
    end
    if !isnothing(gx_wins_pattern_to_filter)
        filter!(s -> s.gx_wins_pattern == gx_wins_pattern_to_filter, filtered_results)
    end

    isempty(filtered_results) && return TriggeredPatternStats{K,L}[] 
    return sort(filtered_results, by = s -> getproperty(s, sort_by), rev = sort_rev)
end

"""
    find_gxs_for_high_performing_patterns_at_period(...)
在指定的 `target_trigger_abs_idx` 期，找出同時滿足 `high_performing_stats` 中定義的高表現模式的 Gx 組合。
"""
function find_gxs_for_high_performing_patterns_at_period(
    gxsprices::Dict{SVector{M, Int8}, GxPrice},
    high_performing_stats::Vector{<:TriggeredPatternStats{K_GXWINS_CONST, L_CUM_CONST}}, 
    target_trigger_abs_idx::Int,
    data_segment_start_abs_idx::Int,
    ::Val{K_GXWINS_CONST},
    ::Val{L_CUM_CONST}
) where {M, K_GXWINS_CONST, L_CUM_CONST}

    @info "開始為期號 $(target_trigger_abs_idx) 查找匹配高表現模式的 Gx 組合..."

    PatternKeyType = Tuple{SVector{K_GXWINS_CONST, Int8}, SVector{L_CUM_CONST, Int8}, SVector{L_CUM_CONST, Int8}, SVector{L_CUM_CONST, Int8}}
    GxListType = Vector{SVector{M, Int8}}
    found_gxs_map = Dict{PatternKeyType, GxListType}()

    idx_in_price_data = target_trigger_abs_idx - data_segment_start_abs_idx + 1
    min_relative_idx_needed = max(K_GXWINS_CONST, L_CUM_CONST)
    if idx_in_price_data < min_relative_idx_needed
        @warn "目標期號 $target_trigger_abs_idx (相對索引 $idx_in_price_data) 過早。需要至少 $min_relative_idx_needed 期的歷史數據 (相對於數據段起始 $data_segment_start_abs_idx)。無法查找 Gx。"
        return found_gxs_map
    end

    target_pattern_keys_set = Set{PatternKeyType}()
    for stat_entry in high_performing_stats
        push!(target_pattern_keys_set, (stat_entry.gx_wins_pattern, stat_entry.cum7_pattern, stat_entry.cum14_pattern, stat_entry.cum21_pattern))
    end

    @info "準備查找 $(length(target_pattern_keys_set)) 個獨特的高表現模式。"
    if isempty(target_pattern_keys_set)
        @info "未提供高表現模式列表，無法查找 Gx。"
        return found_gxs_map
    end

    for (gx, price_data) in gxsprices
        gx_wins_vec = price_data.gx_wins
        if idx_in_price_data > length(gx_wins_vec)
            continue 
        end

        start_idx_gx_wins = idx_in_price_data - K_GXWINS_CONST + 1
        current_gx_wins_pattern = SVector{K_GXWINS_CONST, Int8}(gx_wins_vec[start_idx_gx_wins : idx_in_price_data])

        cum7_vec = price_data.cum7
        cum14_vec = price_data.cum14
        cum21_vec = price_data.cum21
        
        start_idx_cum = idx_in_price_data - L_CUM_CONST + 1
        assoc_cum7_pattern = SVector{L_CUM_CONST, Int8}(cum7_vec[start_idx_cum : idx_in_price_data])
        assoc_cum14_pattern = SVector{L_CUM_CONST, Int8}(cum14_vec[start_idx_cum : idx_in_price_data])
        assoc_cum21_pattern = SVector{L_CUM_CONST, Int8}(cum21_vec[start_idx_cum : idx_in_price_data])

        observed_pattern_key = (current_gx_wins_pattern, assoc_cum7_pattern, assoc_cum14_pattern, assoc_cum21_pattern)

        if observed_pattern_key in target_pattern_keys_set
            if !haskey(found_gxs_map, observed_pattern_key)
                found_gxs_map[observed_pattern_key] = GxListType()
                # 使用 @debug 級別日誌，僅在需要詳細調試時顯示
                @debug "首次匹配到新模式: $(observed_pattern_key) (Gx: $(gx))"
            end
            push!(found_gxs_map[observed_pattern_key], gx)
            @debug "Gx $(gx) 匹配了模式: $(observed_pattern_key)"
        end
    end

    @info "查找完成。共找到 $(length(found_gxs_map)) 個模式匹配，對應總共 $(sum(length, values(found_gxs_map))) 個 Gx 組合。"
    return found_gxs_map
end


"""
    find_gxs_for_pattern_info_collected_at_period(
        gxsprices::Dict{SVector{M, Int8}, GxPrice},
        pattern_info_collected::Vector{Vector{Any}},
        target_trigger_abs_idx::Int,
        data_segment_start_abs_idx::Int,
        ::Val{K_GXWINS_CONST},
        ::Val{L_CUM_CONST}
    ) where {M, K_GXWINS_CONST, L_CUM_CONST}

在指定的 `target_trigger_abs_idx` 期，找出與 `pattern_info_collected` 中定義的模式相匹配的 Gx 組合。
`pattern_info_collected` 是一個向量，其中每個元素是一個包含模式向量列表和元數據的向量。
"""
function find_gxs_for_pattern_info_collected_at_period(
    gxsprices::Dict{SVector{M, Int8}, GxPrice},
    pattern_info_collected::Vector{Vector{Vector{Any}}}, # 例如 pattern_info_collected_1days_3days
    target_trigger_abs_idx::Int,
    data_segment_start_abs_idx::Int,
    ::Val{K_GXWINS_CONST},
    ::Val{L_CUM_CONST}
) where {M, K_GXWINS_CONST, L_CUM_CONST}

    @info "開始為期號 $(target_trigger_abs_idx) 從 'pattern_info_collected' 查找匹配的 Gx 組合..."

    PatternKeyType = Tuple{SVector{K_GXWINS_CONST, Int8}, SVector{L_CUM_CONST, Int8}, SVector{L_CUM_CONST, Int8}, SVector{L_CUM_CONST, Int8}}
    GxListType = Vector{SVector{M, Int8}}
    found_gxs_map = Dict{PatternKeyType, GxListType}()

    idx_in_price_data = target_trigger_abs_idx - data_segment_start_abs_idx + 1
    min_relative_idx_needed = max(K_GXWINS_CONST, L_CUM_CONST)
    if idx_in_price_data < min_relative_idx_needed
        @warn "目標期號 $target_trigger_abs_idx (相對索引 $idx_in_price_data) 過早。需要至少 $min_relative_idx_needed 期的歷史數據 (相對於數據段起始 $data_segment_start_abs_idx)。無法查找 Gx。"
        return found_gxs_map
    end

    target_pattern_keys_set = Set{PatternKeyType}()
    for item in pattern_info_collected
        pattern_vectors_list = Vector{Vector{Int}}(item[1]) # item[1] 是模式定義 Vector{Vector{Int}}

        if length(pattern_vectors_list) != 4
            @warn "跳過項目，因為 pattern_vectors_list 長度不為 4: $(length(pattern_vectors_list))"
            continue
        end

        gx_wins_p_vec, cum7_p_vec, cum14_p_vec, cum21_p_vec = pattern_vectors_list

        if !(length(gx_wins_p_vec) == K_GXWINS_CONST && length(cum7_p_vec) == L_CUM_CONST &&
             length(cum14_p_vec) == L_CUM_CONST && length(cum21_p_vec) == L_CUM_CONST)
            @warn "跳過項目，因為模式長度與 K_GXWINS_CONST ($K_GXWINS_CONST) 或 L_CUM_CONST ($L_CUM_CONST) 不匹配。"
            continue
        end

        key = (SVector{K_GXWINS_CONST, Int8}(Int8.(gx_wins_p_vec)),
               SVector{L_CUM_CONST, Int8}(Int8.(cum7_p_vec)),
               SVector{L_CUM_CONST, Int8}(Int8.(cum14_p_vec)),
               SVector{L_CUM_CONST, Int8}(Int8.(cum21_p_vec)))
        push!(target_pattern_keys_set, key)
    end

    @info "從 'pattern_info_collected' 中成功解析出 $(length(target_pattern_keys_set)) 個獨特的目標模式。"
    if isempty(target_pattern_keys_set)
        @info "從 pattern_info_collected 處理後，未產生目標模式鍵集合，無法查找 Gx。"
        return found_gxs_map
    end

    # 以下邏輯與 find_gxs_for_high_performing_patterns_at_period 中的循環相同
    for (gx, price_data) in gxsprices
        gx_wins_vec = price_data.gx_wins
        if idx_in_price_data > length(gx_wins_vec)
            continue 
        end

        start_idx_gx_wins = idx_in_price_data - K_GXWINS_CONST + 1
        current_gx_wins_pattern = SVector{K_GXWINS_CONST, Int8}(gx_wins_vec[start_idx_gx_wins : idx_in_price_data])

        cum7_vec = price_data.cum7
        cum14_vec = price_data.cum14
        cum21_vec = price_data.cum21
        
        start_idx_cum = idx_in_price_data - L_CUM_CONST + 1
        assoc_cum7_pattern = SVector{L_CUM_CONST, Int8}(cum7_vec[start_idx_cum : idx_in_price_data])
        assoc_cum14_pattern = SVector{L_CUM_CONST, Int8}(cum14_vec[start_idx_cum : idx_in_price_data])
        assoc_cum21_pattern = SVector{L_CUM_CONST, Int8}(cum21_vec[start_idx_cum : idx_in_price_data])

        observed_pattern_key = (current_gx_wins_pattern, assoc_cum7_pattern, assoc_cum14_pattern, assoc_cum21_pattern)

        if observed_pattern_key in target_pattern_keys_set
            if !haskey(found_gxs_map, observed_pattern_key)
                found_gxs_map[observed_pattern_key] = GxListType()
                @debug "首次匹配到新模式 (from collected): $(observed_pattern_key) (Gx: $(gx))"
            end
            push!(found_gxs_map[observed_pattern_key], gx)
            @debug "Gx $(gx) 匹配了模式 (from collected): $(observed_pattern_key)"
        end
    end

    @info "查找完成 (from collected)。共找到 $(length(found_gxs_map)) 個模式匹配，對應總共 $(sum(length, values(found_gxs_map))) 個 Gx 組合。"
    return found_gxs_map
end

# pattern_info = [[[0, 2, 0, 0, 0, 0, 1], [-2, 0, 0, 0, -1, -1, 0], [-3, -1, -1, -1, -2, -2, -1], [-4, -3, -4, -4, -4, -4, -3]],["fan5",1106,[1, 3, 35]]]
# 建立一個函數取得 pattern_info 的 pattern 在 triggered_stats_results 的資訊(了解此pattern在過去的表現)

"""
    find_stats_for_pattern(
        pattern_vectors_list::Vector{Vector{Int}},
        stats_results::Vector{<:TriggeredPatternStats}
    ) -> Union{TriggeredPatternStats, Nothing}

在 `stats_results` 中查找與 `pattern_vectors_list` 中定義的模式相匹配的統計數據。

`pattern_vectors_list` 應為一個包含四個向量的向量，分別代表：
1.  `gx_wins_pattern`
2.  `cum7_pattern`
3.  `cum14_pattern`
4.  `cum21_pattern`

如果找到匹配的 `TriggeredPatternStats` 物件，則返回該物件；否則返回 `nothing`。
"""
function find_stats_for_pattern(
    pattern_vectors_list::Vector{Vector{Int}},
    stats_results::Vector{<:TriggeredPatternStats}
)
    if length(pattern_vectors_list) != 4
        @warn "模式向量列表應包含 gx_wins, cum7, cum14, cum21 共 4 個模式。"
        return nothing
    end

    gx_wins_p_vec = pattern_vectors_list[1]
    cum7_p_vec    = pattern_vectors_list[2]
    cum14_p_vec   = pattern_vectors_list[3]
    cum21_p_vec   = pattern_vectors_list[4]

    K_GXWINS_target = length(gx_wins_p_vec)
    L_CUM_target = length(cum7_p_vec) # 假設 cumX 模式長度一致

    target_gx_wins_pattern = SVector{K_GXWINS_target, Int8}(Int8.(gx_wins_p_vec))
    target_cum7_pattern    = SVector{L_CUM_target, Int8}(Int8.(cum7_p_vec))
    target_cum14_pattern   = SVector{L_CUM_target, Int8}(Int8.(cum14_p_vec))
    target_cum21_pattern   = SVector{L_CUM_target, Int8}(Int8.(cum21_p_vec))

    for stat_entry in stats_results
        if length(stat_entry.gx_wins_pattern) == K_GXWINS_target && length(stat_entry.cum7_pattern) == L_CUM_target && # 檢查長度匹配
           stat_entry.gx_wins_pattern == target_gx_wins_pattern &&
           stat_entry.cum7_pattern    == target_cum7_pattern &&
           stat_entry.cum14_pattern   == target_cum14_pattern &&
           stat_entry.cum21_pattern   == target_cum21_pattern
            return stat_entry
        end
    end
    return nothing # 未找到匹配項
end

# ---



global gxsprices_data # 確保 gxsprices_data 在函數外部也可能被訪問 (如果主執行區塊需要它)
# gxs_combinations = collect(combinations(1:local_param_max_number, local_param_gx_size)) |> 
			  (x -> map(SVector{local_param_gx_size, Int8}, x))
gxs_combinations = collect(combinations(1:39, 3)) |>
                                 (x -> map(SVector{3, Int8}, x))

local_param_game_name = "fan5"
all_numbers_data0 = load_lottery_data("data/$(local_param_game_name).csv", 5)
								 
gxsprices_data = calculate_gxs_gxprice_parallel(all_numbers_data0, gxs_combinations)


gx_wins_lookback = 5
pattern_lookback = 5

triggered_stats_results_1days = analyze_triggered_pattern_performance(
gxsprices_data,
1,
1000, 
1,
3,
Val(gx_wins_lookback),
Val(pattern_lookback)
)

triggered_stats_results_3days = analyze_triggered_pattern_performance(
gxsprices_data,
1,
1000, 
2,
3,
Val(gx_wins_lookback),
Val(pattern_lookback)
)

#= triggered_stats_results_5days = analyze_triggered_pattern_performance(
gxsprices_data,
1,
1000, 
5,
3,
Val(gx_wins_lookback),
Val(pattern_lookback)
) =#

triggered_stats_results = triggered_stats_results_1days

if isempty(triggered_stats_results)
	@warn "對於 AGWL=$(gx_wins_lookback), APL=$(pattern_lookback)，'triggered_stats_results' 為空。跳過此組合的內部參數測試。"
	for hit_rate in hit_rate_options
		for num_instances in num_instances_options
			param_tuple = (AGWL=gx_wins_lookback, APL=pattern_lookback, FMHR=hit_rate, FMNI=num_instances)
			 push!(all_param_set_results, (
				params=param_tuple, 
				avg_profit_ratio=0.0, 
				total_profit=0.0, 
				total_cost=0.0, 
				num_investments=0, 
				total_gx_invested=0 
			))
		end
	end
	# continue 
end

min_hit_rate_threshold = 0.16 # (0.143,0.163,0.2,0.22,0.285)
max_hit_rate_threshold = 0.199 # 設為 nothing 表示沒有上限
min_num_instances_threshold = 20
max_num_instances_threshold = nothing # 設為 nothing 表示沒有上限

custom_sorted_and_filtered_stats_1days = filter_and_sort_triggered_stats(
    triggered_stats_results_1days, 
    sort_by = :hit_rate,
    sort_rev = true,
    min_hit_rate = min_hit_rate_threshold,
    max_hit_rate = max_hit_rate_threshold,
    min_num_instances = min_num_instances_threshold,
    max_num_instances = max_num_instances_threshold,
    gx_wins_pattern_to_filter = nothing 
)

custom_sorted_and_filtered_stats_3days = filter_and_sort_triggered_stats(
	triggered_stats_results_3days, 
	sort_by = :hit_rate,
	sort_rev = true,
	min_hit_rate = min_hit_rate_threshold,
    max_hit_rate = max_hit_rate_threshold,
	min_num_instances = min_num_instances_threshold,
    max_num_instances = max_num_instances_threshold,
	gx_wins_pattern_to_filter = nothing 
)

#= custom_sorted_and_filtered_stats_5days = filter_and_sort_triggered_stats(
    triggered_stats_results_5days, 
    sort_by = :hit_rate,
    sort_rev = true,
    min_hit_rate = hit_rate,
    min_num_instances = num_instances,
    gx_wins_pattern_to_filter = nothing 
) =#


# # --- 獨立執行區塊 (保留用於單獨測試或調試) ---
# profits_standalone = [] # 使用不同名稱以避免與 test_parameter_combinations 中的變量衝突


# 範例：如何使用 find_stats_for_pattern 函數
# 假設 pattern_info_from_user 的結構如您所提供：
# pattern_info_from_user 來自於 圖表觀察
# `E:\obsidian\Julia\JuliaProject\StandAlone\src\A0021_pluto_g3_viewchart_with_gx_wins_patterns_filter_gemini_pro.jl`
begin
pattern_info_collected_1days = [
[[[0, 0, 1, 0, 1], [0, 0, 0, -1, 0], [-1, -1, 0, -1, -1], [-3, -3, -2, -3, -2]],["fan5",1119,[20, 22, 31]]],
[[[0, 1, 1, 0, 1], [-1, 0, 1, 0, 1], [-1, -1, 0, 0, 1], [-3, -2, -1, -1, 0]],["fan5",1122,[20, 22, 31]]],

[[[0, 1, 0, 1, 1], [-2, -1, -2, -1, 0], [-4, -3, -3, -3, -2], [-7, -6, -6, -5, -4]],["fan5",1134,[15, 18, 19]]],
[[[1, 0, 1, 0, 1], [-1, -1, 0, 0, 1], [-1, -1, 0, 0, 0], [-3, -3, -2, -2, -1]],["fan5",1134,[5, 12, 16]]],

[[[0, 0, 0, 0, 1], [-1, -2, -3, -3, -2], [-2, -2, -2, -2, -1], [-3, -3, -4, -4, -3]],["fan5",1134,[10, 11, 13]]],
[[[1, 0, 1, 0, 0], [1, 1, 1, 0, 0], [-2, -2, -1, -1, -1], [-4, -4, -3, -3, -3]],["fan5",1134,[12, 19, 29]]],
[[[1, 1, 0, 1, 0], [-1, -1, -1, 0, 0], [-2, -1, -1, 0, -1], [-4, -3, -3, -3, -3]],["fan5",1134,[15, 35, 37]]],
[[[0, 0, 1, 0, 1], [0, -1, -1, -1, 0], [-2, -2, -1, -1, 0], [-3, -3, -3, -3, -2]],["fan5",1134,[10, 12, 28]]],
[[[0, 0, 1, 1, 1], [-2, -2, -1, 0, 0], [-1, -1, -1, -1, 0], [-2, -2, -2, -1, -1]],["fan5",1134,[7, 25, 27]]],
[[[0, 0, 1, 0, 1], [1, 0, 1, 0, 0], [1, 1, 2, 2, 2], [-2, -2, -1, -1, 0]],["fan5",1134,[30, 34, 39]]],
[[[0, 1, 0, 1, 1], [-3, -2, -2, -1, 0], [-4, -4, -4, -4, -3], [-6, -6, -6, -5, -4]],["fan5",1134,[14, 15, 18]]],
[[[0, 0, 1, 1, 0], [-2, -2, -1, 0, 0], [-4, -4, -3, -2, -2], [-7, -7, -6, -5, -5]],["fan5",1134,[4, 12, 28]]],
[[[0, 0, 1, 1, 1], [-1, -2, -2, -1, 0], [-3, -4, -3, -2, -1], [-4, -4, -4, -3, -2]],["fan5",1134,[4, 8, 10]]],
[[[1, 0, 0, 0, 1], [-2, -2, -2, -2, -1], [-2, -3, -4, -4, -4], [2, 1, 1, -1, -1]],["fan5",1089,[4, 8, 10]]],
[[[0, 1, 0, 0, 1], [0, 1, 0, 0, 1], [0, 0, 0, 0, 1], [-2, -1, -1, -1, -1]],["fan5",1089,[6, 34, 37]]],
[[[0, 0, 1, 1, 1], [0, 0, 1, 1, 1], [-1, -1, 0, 1, 2], [-3, -3, -3, -2, -1]],["fan5",1079,[13, 25, 26]]],
[[[0, 0, 0, 1, 1], [0, 0, 0, 1, 1], [-1, -1, -1, 0, 1], [-3, -3, -3, -3, -2]],["fan5",1078,[13, 25, 26]]],
[[[1, 0, 0, 0, 1], [1, 0, 0, 0, 1], [-1, -1, -1, -1, 0], [-3, -3, -3, -3, -3]],["fan5",1077,[13, 25, 26]]],
[[[0, 0, 0, 0, 1], [2, 2, 1, -1, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 1]],["fan5",1126,[2, 23, 27]]],
[[[0, 1, 1, 0, 1], [-2, -2, -1, -1, 0], [-2, -1, 0, -2, -1], [-3, -2, -1, -1, 0]],["fan5",1134,[9, 18, 30]]],
[[[1, 1, 0, 0, 1], [2, 3, 2, 1, 0], [0, 1, 1, 1, 2], [-2, -1, -1, -1, 0]],["fan5",1127,[23, 25, 37]]],
[[[0, 0, 1, 1, 1], [-2, -2, -1, -1, 0], [-4, -4, -3, -2, -1], [-6, -6, -5, -4, -3]],["fan5",1110,[21, 23, 36]]],
[[[1, 1, 0, 0, 1], [-1, 0, 0, 0, 1], [-2, -1, -2, -2, -2], [-4, -3, -3, -3, -2]],["fan5",1127,[1, 30, 34]]],
[[[1, 0, 1, 0, 1], [-2, -2, -1, -1, 0], [-3, -3, -3, -3, -3], [-5, -5, -4, -4, -3]],["fan5",1127,[1, 19, 34]]],
[[[1, 0, 0, 0, 1], [0, 0, 0, 0, 1], [0, 0, -1, -2, -1], [-1, -1, -1, -1, 0]],["fan5",1138,[25, 27, 28]]],
[[[0, 0, 0, 1, 1], [-1, -2, -2, -1, 0], [-1, -1, -1, -1, -1], [-4, -4, -4, -3, -2]],["fan5",1133,[11, 25, 27]]],

]

# triggered_stats_results_1days 與 triggered_stats_results_3days 均表現良好的模式
pattern_info_collected_1days_3days = [
[[[0, 0, 1, 0, 1], [0, 0, 0, -1, 0], [-1, -1, 0, -1, -1], [-3, -3, -2, -3, -2]],["fan5",1119,[20, 22, 31]]],
[[[0, 1, 1, 0, 1], [-1, 0, 1, 0, 1], [-1, -1, 0, 0, 1], [-3, -2, -1, -1, 0]],["fan5",1122,[20, 22, 31]]],

[[[0, 1, 0, 1, 1], [-2, -1, -2, -1, 0], [-4, -3, -3, -3, -2], [-7, -6, -6, -5, -4]],["fan5",1134,[15, 18, 19]]],
[[[1, 0, 1, 0, 1], [-1, -1, 0, 0, 1], [-1, -1, 0, 0, 0], [-3, -3, -2, -2, -1]],["fan5",1134,[5, 12, 16]]],

[[[0, 0, 0, 0, 1], [-1, -2, -3, -3, -2], [-2, -2, -2, -2, -1], [-3, -3, -4, -4, -3]],["fan5",1134,[10, 11, 13]]],
[[[1, 0, 1, 0, 0], [1, 1, 1, 0, 0], [-2, -2, -1, -1, -1], [-4, -4, -3, -3, -3]],["fan5",1134,[12, 19, 29]]],
[[[1, 1, 0, 1, 0], [-1, -1, -1, 0, 0], [-2, -1, -1, 0, -1], [-4, -3, -3, -3, -3]],["fan5",1134,[15, 35, 37]]],
[[[0, 0, 1, 0, 1], [0, -1, -1, -1, 0], [-2, -2, -1, -1, 0], [-3, -3, -3, -3, -2]],["fan5",1134,[10, 12, 28]]],
[[[0, 0, 1, 1, 1], [-2, -2, -1, 0, 0], [-1, -1, -1, -1, 0], [-2, -2, -2, -1, -1]],["fan5",1134,[7, 25, 27]]],
[[[0, 0, 1, 0, 1], [1, 0, 1, 0, 0], [1, 1, 2, 2, 2], [-2, -2, -1, -1, 0]],["fan5",1134,[30, 34, 39]]],
[[[0, 1, 0, 1, 1], [-3, -2, -2, -1, 0], [-4, -4, -4, -4, -3], [-6, -6, -6, -5, -4]],["fan5",1134,[14, 15, 18]]],
[[[0, 0, 1, 1, 0], [-2, -2, -1, 0, 0], [-4, -4, -3, -2, -2], [-7, -7, -6, -5, -5]],["fan5",1134,[4, 12, 28]]],
[[[0, 0, 1, 1, 1], [-1, -2, -2, -1, 0], [-3, -4, -3, -2, -1], [-4, -4, -4, -3, -2]],["fan5",1134,[4, 8, 10]]],
[[[1, 0, 0, 0, 1], [-2, -2, -2, -2, -1], [-2, -3, -4, -4, -4], [2, 1, 1, -1, -1]],["fan5",1089,[4, 8, 10]]],
[[[0, 1, 0, 0, 1], [0, 1, 0, 0, 1], [0, 0, 0, 0, 1], [-2, -1, -1, -1, -1]],["fan5",1089,[6, 34, 37]]],
[[[0, 0, 1, 1, 1], [0, 0, 1, 1, 1], [-1, -1, 0, 1, 2], [-3, -3, -3, -2, -1]],["fan5",1079,[13, 25, 26]]],
[[[0, 0, 0, 1, 1], [0, 0, 0, 1, 1], [-1, -1, -1, 0, 1], [-3, -3, -3, -3, -2]],["fan5",1078,[13, 25, 26]]],
[[[1, 0, 0, 0, 1], [1, 0, 0, 0, 1], [-1, -1, -1, -1, 0], [-3, -3, -3, -3, -3]],["fan5",1077,[13, 25, 26]]],
[[[0, 0, 0, 0, 1], [2, 2, 1, -1, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 1]],["fan5",1126,[2, 23, 27]]],
[[[0, 1, 1, 0, 1], [-2, -2, -1, -1, 0], [-2, -1, 0, -2, -1], [-3, -2, -1, -1, 0]],["fan5",1134,[9, 18, 30]]],
[[[1, 1, 0, 0, 1], [2, 3, 2, 1, 0], [0, 1, 1, 1, 2], [-2, -1, -1, -1, 0]],["fan5",1127,[23, 25, 37]]],
[[[0, 0, 1, 1, 1], [-2, -2, -1, -1, 0], [-4, -4, -3, -2, -1], [-6, -6, -5, -4, -3]],["fan5",1110,[21, 23, 36]]],
[[[1, 1, 0, 0, 1], [-1, 0, 0, 0, 1], [-2, -1, -2, -2, -2], [-4, -3, -3, -3, -2]],["fan5",1127,[1, 30, 34]]],
[[[1, 0, 1, 0, 1], [-2, -2, -1, -1, 0], [-3, -3, -3, -3, -3], [-5, -5, -4, -4, -3]],["fan5",1127,[1, 19, 34]]],
[[[1, 0, 0, 0, 1], [0, 0, 0, 0, 1], [0, 0, -1, -2, -1], [-1, -1, -1, -1, 0]],["fan5",1138,[25, 27, 28]]],
[[[0, 0, 0, 1, 1], [-1, -2, -2, -1, 0], [-1, -1, -1, -1, -1], [-4, -4, -4, -3, -2]],["fan5",1133,[11, 25, 27]]],

# ---
[[[0, 0, 0, 1, 1], [-3, -3, -3, -2, -1], [-3, -4, -5, -4, -3], [-3, -3, -3, -2, -2]],["fan5",958,[5, 10, 15]]],
[[[0, 0, 1, 1, 1], [-3, -3, -2, -1, 0], [-4, -5, -4, -3, -3], [-3, -3, -2, -2, -1]],["fan5",959,[5, 10, 15]]],
[[[0, 1, 0, 1, 0], [-1, 0, -1, 0, -1], [-3, -2, -2, -1, -1], [-4, -3, -3, -3, -3]],["fan5",945,[4, 25, 33]]],
[[[1, 0, 1, 0, 1], [0, -1, 0, -1, 0], [-2, -2, -1, -1, 0], [-3, -3, -3, -3, -3]],["fan5",946,[4, 25, 33]]],
[[[1, 0, 1, 0, 1], [2, 1, 2, 1, 2], [1, 1, 1, 1, 1], [-1, -1, 0, 0, 1]],["fan5",952,[4, 25, 33]]],
[[[0, 1, 0, 0, 1], [-2, -1, -1, -1, -1], [-3, -2, -2, -3, -3], [-3, -2, -2, -2, -2]],["fan5",1111,[1, 23, 24]]],
[[[1, 1, 0, 1, 1], [-1, 0, 0, 1, 2], [-2, -1, -2, -1, 0], [-4, -3, -3, -3, -2]],["fan5",1113,[21, 23, 36]]],
[[[0, 0, 0, 0, 1], [0, -1, -1, -2, -1], [0, -1, -1, -1, 0], [-1, -2, -2, -2, -1]],["fan5",1079,[26, 27, 33]]],
[[[1, 0, 0, 0, 1], [-1, -1, -2, -2, -1], [-2, -2, -2, -2, -1], [-4, -4, -4, -5, -4]],["fan5",1135,[6, 13, 33]]],
[[[1, 0, 1, 0, 0], [-1, -2, -1, -1, -1], [-3, -3, -3, -3, -3], [-4, -4, -3, -3, -3]],["fan5",1135,[4, 9, 19]]],
[[[1, 1, 0, 0, 1], [0, 1, 0, 0, 1], [-3, -2, -2, -2, -1], [-3, -2, -2, -3, -2]],["fan5",1135,[8, 29, 33]]],
[[[0, 0, 0, 1, 1], [-2, -2, -2, -1, 0], [-4, -4, -5, -4, -3], [-4, -5, -5, -5, -4]],["fan5",1135,[1, 14, 18]]],
[[[1, 0, 0, 0, 1], [-1, -1, -2, -2, -1], [-3, -3, -3, -3, -2], [-1, -2, -3, -3, -2]],["fan5",1131,[27, 33, 36]]],
[[[0, 0, 0, 0, 1], [0, -1, -1, -2, -1], [0, 0, 0, -1, 0], [-1, -1, -1, -1, -1]],["fan5",1126,[23, 26, 29]]],
[[[0, 0, 0, 1, 1], [-2, -3, -3, -2, -1], [-3, -3, -4, -3, -2], [-4, -5, -5, -4, -3]],["fan5",1136,[14, 35, 39]]],
[[[0, 1, 0, 1, 0], [-1, -1, -1, 0, 0], [-2, -1, -2, -1, -2], [-5, -4, -4, -3, -3]],["fan5",1136,[15, 28, 39]]],
[[[0, 1, 1, 0, 0], [0, 1, 1, 0, 0], [-2, -1, 0, 0, 0], [-2, -2, -1, -2, -2]],["fan5",1136,[15, 24, 34]]],
[[[0, 0, 1, 0, 1], [0, 0, 0, -1, 0], [0, -1, 0, 0, 1], [-1, -1, 0, 0, 0]],["fan5",1136,[9, 19, 34]]],
[[[2, 0, 0, 0, 1], [0, 0, 0, 0, 0], [-3, -3, -3, -3, -2], [-4, -4, -4, -4, -3]],["fan5",1136,[8, 12, 14]]],
[[[1, 0, 0, 1, 0], [1, 0, 0, 1, 0], [-1, -1, -1, 0, 0], [-3, -3, -3, -2, -2]],["fan5",1136,[12, 19, 29]]],
[[[1, 0, 0, 0, 1], [0, 0, 0, 0, 0], [-1, -1, -2, -2, -1], [-3, -3, -3, -3, -2]],["fan5",1136,[12, 14, 37]]],
[[[0, 0, 1, 0, 1], [-2, -2, -1, -1, 0], [-1, -2, -1, -2, -1], [0, 0, 0, -1, -1]],["fan5",1136,[7, 9, 10]]],
[[[1, 1, 0, 0, 1], [-1, 0, 0, 0, 1], [-4, -3, -3, -3, -2], [-4, -3, -3, -3, -3]],["fan5",1136,[4, 8, 32]]],
[[[0, 1, 0, 1, 1], [-2, -1, -1, -1, 0], [-4, -3, -3, -2, -1], [-4, -4, -4, -3, -3]],["fan5",1132,[1, 15, 30]]],
[[[0, 0, 0, 0, 1], [0, -1, -2, -2, -1], [-1, -1, -1, -1, -1], [-4, -4, -4, -4, -3]],["fan5",1132,[11, 25, 27]]],
[[[1, 0, 0, 1, 1], [-1, -1, -1, 0, 0], [-2, -2, -2, -1, 0], [-3, -4, -4, -4, -3]],["fan5",1131,[15, 27, 37]]],
[[[0, 0, 1, 1, 1], [-1, -1, 0, 0, 1], [-2, -2, -1, 0, 0], [-4, -4, -4, -3, -2]],["fan5",1132,[15, 27, 37]]],
[[[1, 0, 0, 1, 0], [0, 0, -1, 0, -1], [0, -1, -1, 0, -1], [-2, -2, -2, -1, -1]],["fan5",1132,[15, 17, 25]]],
[[[0, 0, 0, 1, 1], [-2, -2, -2, -2, -1], [-4, -4, -4, -3, -2], [-6, -6, -6, -5, -4]],["fan5",1132,[4, 30, 33]]],
[[[1, 0, 0, 1, 1], [-1, -1, -1, -1, 0], [-2, -2, -3, -2, -1], [-4, -4, -5, -4, -3]],["fan5",1117,[3, 30, 38]]],
[[[1, 0, 0, 0, 1], [0, 0, 0, 0, 1], [-1, -1, -2, -2, -2], [-3, -3, -3, -3, -2]],["fan5",1130,[10, 15, 29]]],
[[[0, 1, 0, 1, 1], [-3, -2, -2, -1, 0], [-6, -5, -5, -4, -3], [-6, -6, -7, -7, -6]],["fan5",1124,[19, 30, 34]]],
[[[0, 0, 1, 1, 1], [-3, -3, -2, -1, 0], [-6, -6, -5, -4, -3], [-5, -5, -5, -5, -4]],["fan5",1141,[24, 25, 28]]],
[[[0, 0, 1, 0, 1], [-3, -3, -2, -2, -1], [-5, -5, -5, -5, -4], [-7, -7, -6, -6, -5]],["fan5",1141,[13, 19, 26]]],


]
end


    pattern_info = [[[0, 0, 0, 1, 1], [-2, -2, -2, -1, 0], [-2, -3, -3, -2, -2], [-3, -3, -3, -3, -2]],["fan5",1109,[18, 20, 21]]]
begin
# # 從 pattern_info_from_user 中提取模式定義部分
# target_pattern_definition = Vector{Vector{Int64}}(pattern_info_from_user[1][1])
target_pattern_definition = Vector{Vector{Int64}}(pattern_info[1])
# # 假設 triggered_stats_results 已經被計算出來
matched_stats_1days = find_stats_for_pattern(target_pattern_definition, triggered_stats_results_1days)
println(matched_stats_1days)

matched_stats_3days = find_stats_for_pattern(target_pattern_definition, triggered_stats_results_3days)
println(matched_stats_3days)
end

begin
    selected_row = 1148
	# gxs_for_high_performing_patterns = find_gxs_for_high_performing_patterns_at_period(
									# gxsprices_data,
									# custom_sorted_and_filtered_stats_1days,
									# selected_row,
									# 1, # 傳遞 gxsprices_data 所用數據的實際起始索引
									# Val(gx_wins_lookback),
									# Val(pattern_lookback)
								# )
	# end

	gxs_for_high_performing_patterns_1days = find_gxs_for_high_performing_patterns_at_period(
									gxsprices_data,
									custom_sorted_and_filtered_stats_1days,
									selected_row,
									1, # 傳遞 gxsprices_data 所用數據的實際起始索引
									Val(gx_wins_lookback),
									Val(pattern_lookback)
								)
								
	gxs_for_high_performing_patterns_3days = find_gxs_for_high_performing_patterns_at_period(
									gxsprices_data,
									custom_sorted_and_filtered_stats_3days,
									selected_row,
									1, # 傳遞 gxsprices_data 所用數據的實際起始索引
									Val(gx_wins_lookback),
									Val(pattern_lookback)
								)							

    gxs_for_pattern_info_collected_1days = find_gxs_for_pattern_info_collected_at_period(
        gxsprices_data,
        pattern_info_collected_1days,
        selected_row,
        1, # 傳遞 gxsprices_data 所用數據的實際起始索引
        Val(gx_wins_lookback),
        Val(pattern_lookback)
    )

    gxs_for_pattern_info_collected_1days_3days = find_gxs_for_pattern_info_collected_at_period(
        gxsprices_data,
        pattern_info_collected_1days_3days,
        selected_row,
        1, # 傳遞 gxsprices_data 所用數據的實際起始索引
        Val(gx_wins_lookback),
        Val(pattern_lookback)
    )
	
# 假設 gxs_for_high_performing_patterns 已經被賦值

#= if !isempty(gxs_for_high_performing_patterns_1days)
    println("找到的高表現模式及其對應的 Gx 組合：")
    for (pattern_key, gx_list) in gxs_for_high_performing_patterns_1days
        # pattern_key 是觸發模式的組合 (SVector{K_GXWINS, Int8}, SVector{L_CUM, Int8}, ...)
        # gx_list 是一個 Vector{SVector{M, Int8}}，其中每個 SVector 就是一個 Gx 組合
        println("模式: ", pattern_key)
        println("  匹配的 Gx 組合 (名單): ", gx_list)
        # 在這裡，您可以對 gx_list (即 Gx 名單) 進行後續處理
    end
else
    println("在指定期數 $(selected_row) 未找到任何高表現模式對應的 Gx 組合。")
end =#

# 如果您想獲取特定模式 (如果存在於結果中) 的 Gx 列表：
# 假設 first_pattern_key 是字典中的一個鍵
# if !isempty(gxs_for_high_performing_patterns)
#     a_pattern_key = first(keys(gxs_for_high_performing_patterns)) # 取得一個示例鍵
#     specific_gx_list = gxs_for_high_performing_patterns[a_pattern_key]
#     println("\n特定模式 $a_pattern_key 的 Gx 列表: $specific_gx_list")
# end

# 收集所有找到的 Gx 組合到一個單一的向量中
# all_matched_gxs = vcat(collect(values(gxs_for_high_performing_patterns))...)
# all_matched_gxs = map(svec -> Vector(Int64.(svec)), all_matched_gxs)
    # # This first converts elements of svec to Int64 (resulting in SVector{M, Int64})
    # # and then converts that SVector{M, Int64} to a Vector{Int64}.
all_matched_gxs_1days = vcat(collect(values(gxs_for_high_performing_patterns_1days))...)
all_matched_gxs_3days = vcat(collect(values(gxs_for_high_performing_patterns_3days))...)
	
all_matched_gxs_1days = map(svec -> Vector(Int64.(svec)), all_matched_gxs_1days)
all_matched_gxs_3days = map(svec -> Vector(Int64.(svec)), all_matched_gxs_3days)
# all_matched_gxs = intersect(all_matched_gxs_1days,all_matched_gxs_3days)
all_matched_gxs = all_matched_gxs_1days
	
# 為了日誌和檔名，建立範圍的字串表示
hit_rate_range_str = "min_$(min_hit_rate_threshold)" * (!isnothing(max_hit_rate_threshold) ? "_max_$(max_hit_rate_threshold)" : "")
num_instances_range_str = "min_$(min_num_instances_threshold)" * (!isnothing(max_num_instances_threshold) ? "_max_$(max_num_instances_threshold)" : "")

foreach(println, all_matched_gxs)
# 現在 all_matched_gxs 是一個 Vector{SVector{M, Int8}}，包含了所有匹配的 Gx 組合
# 您可以對 all_matched_gxs 進行後續操作或儲存到檔案
println("\n在期數 $(selected_row) 總共找到 $(length(all_matched_gxs)) 個匹配的高表現 Gx 組合。")

# # 使用 JLD2 儲存 (推薦)

serialize("$(local_param_game_name)_all_matched_gxs_at_period_$(selected_row)_hit_rate_$(hit_rate_range_str)_num_instances_$(num_instances_range_str)_gx_wins_lookback_$(gx_wins_lookback)_pattern_lookback_$(pattern_lookback).jls", all_matched_gxs)
# jldsave("$(local_param_game_name)_all_matched_gxs_at_period_$(selected_row).jld2"; all_matched_gxs)
@info "all_matched_gxs 已儲存到 $(local_param_game_name)_all_matched_gxs_at_period_$(selected_row)_hit_rate_$(hit_rate_range_str)_num_instances_$(num_instances_range_str)_gx_wins_lookback_$(gx_wins_lookback)_pattern_lookback_$(pattern_lookback).jls"

# using JLD2
# loaded_gxs = load("all_matched_gxs_at_period_1131.jld2", "all_matched_gxs")
# # loaded_gxs 將會是原始的 Vector{SVector{M, Int8}}

# # 或者，儲存為 CSV 檔案
# using CSV, DataFrames # 確保已載入套件
# CSV.write("all_matched_gxs_at_period_1131.csv", DataFrame(gx = all_matched_gxs)) # SVector 會被轉為字串，例如 "[1, 2, 3]"
# @info "all_matched_gxs 已儲存到 all_matched_gxs_at_period_1131.csv"

if selected_row < length(all_numbers_data0)
    next_wins_numbers = intersect.(all_matched_gxs,[all_numbers_data0[selected_row+1]])
    num_invest_g3 = length(all_matched_gxs)
    cost = round(num_invest_g3 * 3 * 30.4, digits=2)
    return_money = round(sum(length.(next_wins_numbers)) * 212, digits=2)
    profit = round(return_money - cost, digits=2)
    profit_ratio = round(profit / cost, digits=4)

    println("$(local_param_game_name)_all_matched_gxs_at_period_$(selected_row)_hit_rate_$(hit_rate_range_str)_num_instances_$(num_instances_range_str)_num_invest_g3_$(num_invest_g3)_Return_money_$(return_money)_Cost_$(cost)_Profit_$(profit)_Profit_Ratio_$(profit_ratio)")
    println("num_invest_g3: $(num_invest_g3)")
    println("Return_money: $(return_money)")
    println("Cost: $(cost)")
    println("Profit: $(profit)")
    println("Profit Ratio: $(profit_ratio)")
end

end

# begin
# all_matched_gxs_1days = vcat(collect(values(gxs_for_pattern_info_collected_1days_3days))...)
# all_matched_gxs = map(svec -> Vector(Int64.(svec)), all_matched_gxs_1days)

# foreach(println, all_matched_gxs)
# # 現在 all_matched_gxs 是一個 Vector{SVector{M, Int8}}，包含了所有匹配的 Gx 組合
# # 您可以對 all_matched_gxs 進行後續操作或儲存到檔案
# println("\n在期數 $(selected_row) 總共找到 $(length(all_matched_gxs)) 個匹配的高表現 Gx 組合。")

# # # 使用 JLD2 儲存 (推薦)

# serialize("$(local_param_game_name)_all_matched_gxs_at_period_$(selected_row)_hit_rate_$(hit_rate)_num_instances_$(num_instances)_gx_wins_lookback_$(gx_wins_lookback)_pattern_lookback_$(pattern_lookback)_selected_patterns.jls", all_matched_gxs)
# # jldsave("$(local_param_game_name)_all_matched_gxs_at_period_$(selected_row).jld2"; all_matched_gxs)
# @info "all_matched_gxs 已儲存到 $(local_param_game_name)_all_matched_gxs_at_period_$(selected_row)_hit_rate_$(hit_rate)_num_instances_$(num_instances)_gx_wins_lookback_$(gx_wins_lookback)_pattern_lookback_$(pattern_lookback)_selected_patterns.jls"

# end


# to-do list
# - [x] 將 hit_rate 與 num_instances 閥值 改成最小值與最大值(選定範圍內的植)。
# - [x] 將 `analyze_triggered_pattern_performance` 函式重構成更小的輔助函式以提高可讀性。
# - [ ] 在腳本中加入一個新的工作流程，自動化尋找最佳的 `hit_rate` 和 `num_instances` 範圍。
# - [x] 為 `find_gxs_for_high_performing_patterns_at_period` 函式增加日誌記錄功能，以便追蹤其執行過程。
# - [x] 為 `find_gxs_for_pattern_info_collected_at_period` 函式增加日誌記錄功能。
# - [ ] 請將腳本中所有手動設定的參數（如 `game_name`, `selected_row`）移到一個集中的設定區塊，以方便修改。