# 彩票模式分析工具 - 快速參考

## 🚀 快速啟動
```bash
cd StandAlone
julia src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl
```

## 📋 功能選單

| 選項 | 功能 | 用途 |
|------|------|------|
| 1 | 查找出球率最高的模式 | 發現最佳歷史模式 |
| 2 | 使用特定模式進行回測 | 驗證模式可靠性 |
| 3 | 分析特定模式的表現 | 深度模式分析 |
| 4 | 查找匹配模式的 Gx 組合 | 找出符合條件的組合 |
| 5 | 預測下一期中獎號碼 | 號碼預測 |
| 6 | 分析期號模式表現並存檔 | 特定期號分析 |
| 7 | 全面歷史表現分析 | 完整數據分析 |
| 0 | 退出 | 結束程序 |

## 🎯 常用操作

### 新手推薦流程
```
1. 選擇功能 1 → 找出最佳模式
2. 選擇功能 3 → 分析最佳模式
3. 選擇功能 5 → 預測下期號碼
```

### 專業分析流程
```
1. 選擇功能 7 → 生成完整分析報告
2. 選擇功能 6 → 針對特定期號深度分析
3. 選擇功能 2 → 回測驗證策略
```

## 📊 輸入格式

### 模式格式
- **正確**：`0,1,0,1,0` 或 `0,2,0,2,2,1,0,0,0`
- **錯誤**：`0 1 0 1 0` 或 `0-1-0-1-0`

### 期號格式
- **正確**：`500` 或 `1000`
- **錯誤**：`500.0` 或 `第500期`

### 出球率格式
- **正確**：`0.25` 或 `0.5`
- **錯誤**：`25%` 或 `0,25`

## 📁 輸出文件

### 文件位置
```
results/
├── gx_pattern_analysis_*.csv          # 功能 6 輸出
├── gx_pattern_historical_*.csv        # 功能 7 輸出
└── 其他分析文件
```

### CSV 欄位說明
- `Rank`: 排名
- `Gx_Combination`: Gx 組合 (如 [1,2,3])
- `Pattern`: 模式 (如 [0,1,0,1,0])
- `Hit_Rate`: 出球率 (0.0-1.0)
- `Total_Occurrences`: 觸發次數

## ⚙️ 重要參數

### 預設配置
```julia
max_number = 39              # 號碼範圍 1-39
numbers_in_draw = 5          # 每期 5 個號碼
gx_size = 3                  # Gx 組合大小
wins_pattern_length = 12     # 模式長度
n_future_draws = 2           # 預測期數
```

### 過濾建議
- **最小觸發次數**：5-10 (新手) / 15-20 (專業)
- **最小出球率**：0.15-0.25
- **最大出球率**：0.8-1.0

## 🔧 故障排除

### 常見錯誤
| 錯誤信息 | 原因 | 解決方案 |
|----------|------|----------|
| `數據文件不存在` | 缺少 data/fan5.csv | 檢查文件路徑 |
| `輸入格式不正確` | 格式錯誤 | 使用逗號分隔 |
| `無效的目標期號` | 期號超出範圍 | 檢查期號範圍 |
| `OutOfMemoryError` | 記憶體不足 | 減少參數或增加記憶體 |

### 性能優化
```bash
# 設置多線程
export JULIA_NUM_THREADS=4
julia -t 4 src/analyze_*.jl
```

## 📈 使用技巧

### 模式選擇建議
- **短模式** (3-5): 觸發頻率高，但準確性較低
- **中模式** (8-12): 平衡觸發頻率和準確性
- **長模式** (15+): 觸發頻率低，但準確性較高

### 分析策略
1. **探索階段**：使用功能 1 找出候選模式
2. **驗證階段**：使用功能 2、3 驗證模式
3. **應用階段**：使用功能 5 進行預測
4. **深度分析**：使用功能 6、7 生成報告

### 結果解讀
- **出球率 > 30%**：優秀模式，值得重點關注
- **出球率 20-30%**：良好模式，可以考慮使用
- **出球率 < 20%**：一般模式，謹慎使用
- **觸發次數 < 5**：數據不足，結果不可靠

## 🎲 實戰範例

### 範例 1：找出最佳投注組合
```
步驟 1: 選擇功能 1，找出最佳模式
步驟 2: 記錄前 3 名模式
步驟 3: 選擇功能 5，使用最佳模式預測
步驟 4: 根據預測結果選擇投注號碼
```

### 範例 2：驗證投注策略
```
步驟 1: 選擇功能 3，分析目標模式
步驟 2: 檢查觸發次數是否 ≥ 10
步驟 3: 檢查出球率是否 ≥ 25%
步驟 4: 選擇功能 2，在多個期號回測
```

### 範例 3：生成分析報告
```
步驟 1: 選擇功能 7，設置目標期號
步驟 2: 設置過濾條件 (觸發次數 ≥ 5, 出球率 ≥ 0.2)
步驟 3: 導出 CSV 文件
步驟 4: 在 Excel 中進一步分析
```

## 📞 支援資源

### 文件資源
- 📖 **完整手冊**：`使用者手冊.md`
- 🔍 **源代碼**：`src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl`
- 📊 **範例數據**：`data/fan5.csv`

### 學習路徑
1. **初學者**：閱讀快速參考 → 嘗試功能 1、5
2. **進階用戶**：閱讀完整手冊 → 使用所有功能
3. **專業用戶**：研究源代碼 → 自定義配置

---
**提示**：建議先用小數據集測試，熟悉操作後再進行大規模分析。
