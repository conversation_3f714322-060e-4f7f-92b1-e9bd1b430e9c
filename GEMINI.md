- Think step by step, must keep each draft with 5 words at most for each thinking step , Return the answer at the end of the response after a separator ####."
- <PERSON><PERSON> respond in Traditional Chinese.(zh-hant-tw)，
- 每次修改都要完整呈現代碼。
- If Possible, write short codes better than long codes.
- 優化代碼 : 使用Julia推薦方式
- 使用共享模組`LotteryAnalysisUtils.jl`
- 修改腳本讓腳本更清新更有組織，避免重複性代碼
- 使用Julia代碼語法
- 我總是以繁體中文回答
- 注意函數參數類型是否匹配。
- Julia 的參數類型系統和多重分派機制
- 從錯誤中學習並快速調整方向，這是解決編程問題的關鍵能力。
- 良好的代碼組織和命名約定對於維護和擴展代碼非常重要。


1. **特定領域知識指導**：
    - Julia 語言特定的最佳實踐和性能優化技巧
    - 彩票預測/統計分析領域的專業術語和常見模式
    - 數據科學和機器學習在預測模型中的應用原則
2. **代碼結構與架構指導**：
    - 模塊化設計原則和函數拆分建議
    - 錯誤處理和邊界情況的處理策略
    - 測試策略和代碼可維護性建議
3. **性能優化指導**：
    - Julia 特有的性能陷阱和避免方法
    - 大數據集處理的記憶體優化技巧
    - 並行計算和多線程最佳實踐
4. **用戶交互指導**：
    - 如何設計更友好的命令行輸出格式
    - 結果可視化的最佳實踐
    - 用戶體驗設計原則
5. **項目特定上下文**：
    - 更多關於項目整體架構的信息
    - 各個模塊之間的依賴關係
    - 長期開發目標和擴展計劃
6. **代碼審查標準**：
    - 代碼質量評估標準
    - 常見反模式和如何避免
    - 代碼文檔和註釋的最佳實踐
7. **優化代碼結構**：
	- 使用 Julia 推薦的類型註解和函數簽名
	- 保持代碼簡潔和高效	

#### 結論

這些額外的指導可以幫助我提供更全面、更有針對性的協助，特別是在處理特定領域的複雜問題時。理解更廣泛的項目上下文和領域知識可以讓我的建議更加實用和相關。同時，關於代碼質量和最佳實踐的明確標準可以確保我提供的解決方案不僅解決當前問題，還能為長期的代碼健康和可維護性做出貢獻。

