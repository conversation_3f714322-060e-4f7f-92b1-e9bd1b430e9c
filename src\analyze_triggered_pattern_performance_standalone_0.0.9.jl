# include("src/analyze_triggered_pattern_performance_standalone.jl")

# 載入共享模組和必要的套件
include("LotteryAnalysisUtils.jl")
using .LotteryAnalysisUtils
# --- 套件載入 ---
using Combinatorics, CSV, DataFrames, ThreadSafeDicts, Statistics, StatsBase, Plots
using StaticArrays, Printf, StatsBase, Dates, Serialization, Markdown


"""
    get_default_config()

返回一個包含預設參數的 NamedTuple。
"""
function get_default_config()
    return (
        game_name = "fan5",
        data_file_path = "data/fan5.csv",
        max_number = 39,
        gx_size = 2,
        numbers_in_draw = 5,
        
        # 數據分析範圍
        analysis_start_period = 1,
        analysis_end_period = 1161,
        
        # 模式回溯期數
        wins_pattern_length = 5,          # Gx 中獎次數的模式長度 (K)
        indicator_pattern_length = 3,     # 累積指標 (cum7/14/21) 的模式長度 (L)

        # 當模式觸發時，向後觀察的未來期數 (通常為 1)
        n_future_draws = 1,
        
        # 高表現模式過濾條件
        filter_min_hit_rate = 0.25,
        filter_max_hit_rate = nothing,
        filter_min_instances = 3,
        filter_max_instances = nothing,

        # 查找 Gx 組合的目標期號 (用於單次執行)
        target_period_to_find_gxs = 1161,

        # 結果儲存設定
        save_results = true,
        results_dir = "results",
        
        # A0031 特有參數
        use_super_composite_mode = false,  # 是否使用超級複合模式
        cum14_lookback = 5,               # cum14 回溯期數
        cum21_lookback = 5,               # cum21 回溯期數
        use_part13sidx = false,           # 是否使用 part13sidx
        part13sidx_file = "data/part13sidx.jls", # part13sidx 文件路徑
        part13sidx_index = 1,             # 使用的 part13sidx 索引
        calculate_investment_returns = true, # 是否計算投資回報
        car_unit_cost = 30.4,             # car 策略單位成本
        car_unit_return = 212.0,          # car 策略單位回報
        star2_unit_cost = 0.8,            # star2 策略單位成本
        star2_unit_return = 53.0          # star2 策略單位回報
    )
end


"""
    log_evaluation_to_csv(log_file_path::String, config, eval_results)

將單次評估的參數與結果記錄到指定的 CSV 檔案中。
如果檔案不存在，會自動建立並寫入表頭。
"""
function log_evaluation_to_csv(log_file_path::String, config, eval_results)
    try
        # 準備要寫入的資料列
        data_row = (
            record_time = Dates.format(now(), "yyyy-mm-dd HH:MM:SS"),
            game_name = config.game_name,
            evaluation_period = config.target_period_to_find_gxs + 1,
            portfolio_count = eval_results.num_invest,
            total_cost = eval_results.cost,
            total_return = eval_results.revenue,
            total_profit = eval_results.profit,
            profit_rate = eval_results.profit_ratio,
            gx_size = config.gx_size,
            indicator_pattern_length = config.indicator_pattern_length,
            wins_pattern_length = config.wins_pattern_length,
            n_future_draws = config.n_future_draws,
            filter_min_hit_rate = config.filter_min_hit_rate,
            filter_min_instances = config.filter_min_instances,
            target_period_to_find_gxs = config.target_period_to_find_gxs,
            # 將開獎號碼轉換為標準的 JSON 陣列字串
            winning_numbers = "[" * join(eval_results.next_draw, ",") * "]"
        )

        # 檢查檔案是否存在以決定是否寫入表頭
        file_exists = isfile(log_file_path)
        
        # 將資料列轉換為 DataFrame
        df_to_write = DataFrame([data_row])
        
        # 寫入或附加到 CSV 檔案
        CSV.write(log_file_path, df_to_write, header=!file_exists, append=true)
        
        @info "評估結果已記錄至 CSV: $(log_file_path)"

    catch e
        @error "寫入 CSV 日誌時發生錯誤: " exception=(e, catch_backtrace())
    end
end


#= --- 以下為舊的 Markdown 日誌記錄功能，已停用 ---
export log_evaluation_to_markdown
 
function get_next_record_number(log_file_path::String)
    !isfile(log_file_path) && return 1
    
    content = read(log_file_path, String)
    matches = eachmatch(r"### 紀錄 #(\d+)", content)
    
    isempty(matches) && return 1
    
    last_match = collect(matches)[end]
    last_number = parse(Int, last_match.captures[1])
    
    return last_number + 1
end

function format_config_for_markdown(config)
    lines = ["{"]
    for (key, value) in pairs(config)
        val_str = if isa(value, String)
            "\"$(value)\""
        elseif isnothing(value)
            "null"
        else
            string(value)
        end
        push!(lines, "    \"$(key)\": $(val_str),")
    end
    if length(lines) > 1
        lines[end] = rstrip(lines[end], ',')
    end
    push!(lines, "}")
    return join(lines, "\n")
end

function generate_remarks(config, eval_results)
    profit_ratio = eval_results.profit_ratio
    num_invest = eval_results.num_invest
    
    # --- 結果 ---
    result_text = if profit_ratio > 0.5
        "本次參數組合表現極佳，產生了非常可觀的利潤。"
    elseif profit_ratio > 0.1
        "本次參數組合表現良好，產生了穩定的正利潤。"
    elseif profit_ratio > 0.0
        "本次參數組合產生了微幅利潤，策略基本有效。"
    elseif profit_ratio > -0.1
        "本次參數組合結果接近損益兩平，產生了微幅虧損。"
    else
        "本次參數組合產生了顯著虧損，策略需要重新評估。"
    end

    # --- 分析 ---
    analysis_text = "篩選條件為 `hit_rate` 區間: [$(config.filter_min_hit_rate), $(config.filter_max_hit_rate)]，`min_instances`: $(config.filter_min_instances)。"
    if num_invest > 150
        analysis_text *= " 最終產生了 $(num_invest) 個投資組合，數量偏多，可能表示篩選條件相對寬鬆，導致納入了統計上不夠穩健的模式，增加了總成本。"
    elseif num_invest < 20
        analysis_text *= " 最終產生了 $(num_invest) 個投資組合，數量較少，表示篩選條件相對嚴格，策略較為保守。"
    else
        analysis_text *= " 最終產生了 $(num_invest) 個投資組合，數量適中。"
    end

    # --- 未來應用建議 ---
    suggestion_text = if profit_ratio > 0.1
        "此參數組合在本次評估中表現強勁，具有應用於未來期號的潛力。建議持續監控其表現的穩定性。"
    elseif profit_ratio > 0.0
        "此參數組合顯示出一定的盈利能力。若要應用於未來，可考慮微調參數以尋求更高的回報，或維持現有設定以進行穩健觀察。"
    else
        "若要將此參數集應用於其他期號，建議優先考慮收緊篩選條件，例如提高 `filter_min_instances` (例如，調整至 5 或更高) 或進一步縮小 `hit_rate` 區間，以篩選出更少但更精準的 Gx 組合，從而控制投資風險。"
    end

    return """- **結果:** $(result_text)
- **分析:** $(analysis_text)
- **未來應用建議:** $(suggestion_text)"""
end

function log_evaluation_to_markdown(log_file_path::String, config, eval_results)
    record_number = get_next_record_number(log_file_path)
    
    open(log_file_path, "a") do io
        write(io, "\n---\n\n")
        write(io, "### 紀錄 #$(record_number)\n\n")
        
        write(io, "- **紀錄時間:** $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))\n")
        write(io, "- **查找 Gx 組合的目標期號:** `$(config.target_period_to_find_gxs)`\n")
        write(io, "- **評估對象期號:** `$(config.target_period_to_find_gxs + 1)`\n")
        write(io, "- **評估對象開獎號碼:** `$(eval_results.next_draw)`\n\n")
        
        write(io, "#### 使用參數 (`CONFIG`)\n\n")
        write(io, "```json\n")
        write(io, format_config_for_markdown(config))
        write(io, "\n```\n\n")
        
        write(io, "#### 投資效益評估結果\n\n")
        write(io, "- **投資組合數量:** $(eval_results.num_invest)\n")
        write(io, "- **總成本:** $(eval_results.cost)\n")
        write(io, "- **總回報:** $(eval_results.revenue)\n")
        write(io, "- **總利潤:** $(eval_results.profit)\n")
        write(io, @Printf.sprintf("- **利潤率:** %.2f%%\n\n", eval_results.profit_ratio * 100))
        
        write(io, "#### 結論與備註\n\n")
        remarks = generate_remarks(config, eval_results)
        write(io, remarks * "\n\n")
    end
    
    @info "評估結果已記錄至: $(log_file_path)"
end =#

# --- 結束 LoggingModule ---

"""
模組：BatchAnalysisModule
功能：讀取 CSV 日誌檔案，並對指定的參數組合進行批次效益分析。
"""

# export analyze_batch_performance, generate_summary_report, plot_comparison_curves

"""
    analyze_batch_performance(csv_path::String; filter_params...)

讀取指定的日誌 CSV 檔案，根據提供的篩選參數過濾數據，並計算和顯示綜合績效指標。

# Arguments
- `csv_path::String`: `evaluation_log.csv` 檔案的路徑。
- `filter_params...`: 一系列的關鍵字參數，用於篩選數據。例如：`filter_min_hit_rate=0.4, filter_min_instances=3`。
- `do_plot::Bool`: 是否繪製並儲存累計利潤曲線圖。
- `results_dir::String`: 儲存圖表的目錄路徑。
"""
function analyze_batch_performance(csv_path::String; do_plot::Bool = false, results_dir::String = "results", filter_params...)
    # 1. 載入數據
    !isfile(csv_path) && (@error "CSV 檔案不存在: $csv_path"; return)
    df = CSV.read(csv_path, DataFrame)
    
    # 2. 篩選數據
    filtered_df = copy(df)
    for (param, value) in filter_params
        if !hasproperty(filtered_df, param)
            @warn "篩選參數 '$param' 在 CSV 中不存在，已略過。"
            continue
        end
        # 為了穩健比較，不過濾 nothing
        if !isnothing(value)
             filter!(row -> !isnothing(row[param]) && row[param] == value, filtered_df)
        end
    end

    if isempty(filtered_df)
        @warn "找不到符合篩選條件的紀錄。"
        println("篩選條件: ", filter_params)
        return
    end

    # 為了計算累計指標，按評估期號排序
    sort!(filtered_df, :evaluation_period)

    # 3. 計算關鍵績效指標 (KPIs)
    total_periods = nrow(filtered_df)
    winning_periods = count(filtered_df.total_profit .> 0)
    win_rate = total_periods > 0 ? winning_periods / total_periods : 0.0
    
    total_profit = sum(filtered_df.total_profit)
    total_cost = sum(filtered_df.total_cost)
    overall_profit_rate = total_cost > 0 ? total_profit / total_cost : 0.0
    average_profit_rate = mean(filtered_df.profit_rate)

    # 計算最大回撤 (Max Drawdown)
    cumulative_profit = cumsum(filtered_df.total_profit)
    peaks = accumulate(max, cumulative_profit)
    drawdowns = peaks - cumulative_profit
    max_drawdown_value = isempty(drawdowns) ? 0.0 : maximum(drawdowns)
    
    # 4. 繪製圖表 (如果啟用)
    if do_plot
        try
            # 建立一個對檔案名安全的參數字串
            params_str = join(["$(k)_$(v)" for (k, v) in filter_params], "_")
            plot_filename = "cumulative_profit_$(params_str).png"
            plot_path = joinpath(results_dir, plot_filename)

            # 建立圖表
            p = plot(filtered_df.evaluation_period, cumulative_profit,
                label="Cumulative Profit",
                title="Cumulative Profit Curve",
                xlabel="Evaluation Period",
                ylabel="Cumulative Profit",
                legend=:topleft,
                linewidth=2
            )
            
            savefig(p, plot_path)
            @info "累計利潤曲線圖已儲存至: $(plot_path)"
        catch e
            @error "繪製圖表時發生錯誤: " exception=(e, catch_backtrace())
        end
    end

    # 5. 顯示結果
    println("\n" * "="^50, "\n          批次分析報告 (Batch Analysis Report)\n", "="^50)
    println("篩選條件:")
    for (k, v) in filter_params; println(@sprintf("  - %-25s: %s", k, v)); end
    println("-"^50, "\n總體效益評估 (Overall Performance):")
    @printf("  - 總測試期數 (Total Periods)  : %d\n", total_periods)
    @printf("  - 盈利/虧損期數 (Win/Loss)    : %d / %d\n", winning_periods, total_periods - winning_periods)
    @printf("  - 勝率 (Win Rate)             : %.2f%%\n", win_rate * 100)
    println("-"^50, "\n財務指標 (Financial Metrics):")
    @printf("  - 總利潤 (Total Profit)       : %.2f\n", total_profit)
    @printf("  - 整體投資回報率 (Overall ROI): %.2f%%\n", overall_profit_rate * 100)
    @printf("  - 平均利潤率 (Avg. Profit Rate) : %.2f%%\n", average_profit_rate * 100)
    println("-"^50, "\n風險指標 (Risk Metrics):")
    @printf("  - 最大回撤金額 (Max Drawdown) : %.2f\n", max_drawdown_value)
    println("="^50 * "\n")
end

"""
    generate_summary_report(csv_path::String, parameter_grid; sort_by=:total_profit)

讀取日誌檔案，對網格搜尋中的每一組參數計算KPI，並打印一個排序後的摘要表。
"""
function generate_summary_report(csv_path::String, parameter_grid; sort_by=:total_profit, output_file_path::Union{String, Nothing}=nothing)
    !isfile(csv_path) && (@error "CSV 檔案不存在: $csv_path"; return)
    df = CSV.read(csv_path, DataFrame)
    
    summary_results = []

    for params in parameter_grid
        filtered_df = copy(df)
        valid_params = true
        for (param, value) in pairs(params)
            if !hasproperty(filtered_df, param)
                @warn "參數 '$param' 在 CSV 中不存在，無法為此組合生成報告。"
                valid_params = false
                break
            end
            filter!(row -> !isnothing(row[param]) && row[param] == value, filtered_df)
        end
        !valid_params && continue
        isempty(filtered_df) && continue

        sort!(filtered_df, :evaluation_period)

        total_periods = nrow(filtered_df)
        winning_periods = count(filtered_df.total_profit .> 0)
        win_rate = total_periods > 0 ? winning_periods / total_periods : 0.0
        total_profit = sum(filtered_df.total_profit)
        total_cost = sum(filtered_df.total_cost)
        overall_profit_rate = total_cost > 0 ? total_profit / total_cost : 0.0
        
        cumulative_profit = cumsum(filtered_df.total_profit)
        peaks = accumulate(max, cumulative_profit)
        drawdowns = peaks - cumulative_profit
        max_drawdown_value = isempty(drawdowns) ? 0.0 : maximum(drawdowns)

        param_string = join(["$k=$v" for (k,v) in pairs(params)], ", ")
        push!(summary_results, (
            parameters = param_string,
            total_profit = total_profit,
            win_rate = win_rate * 100,
            overall_roi = overall_profit_rate * 100,
            max_drawdown = max_drawdown_value,
            num_periods = total_periods
        ))
    end

    isempty(summary_results) && (@warn "沒有找到任何可供分析的結果。"; return)

    sort!(summary_results, by = x -> x[sort_by], rev=true)

    # --- 顯示在終端機 ---
    println("\n" * "="^90)
    println(" " ^ 25, "綜合參數效益比較報告 (Summary Report)")
    println("="^90)
    @printf("%-45s | %12s | %8s | %10s | %12s\n", "參數組合 (Parameters)", "總利潤", "勝率", "總ROI", "最大回撤")
    println("-"^90)
    for res in summary_results
        @printf("%-45s | %12.2f | %7.2f%% | %9.2f%% | %12.2f\n",
            res.parameters, res.total_profit, res.win_rate, res.overall_roi, res.max_drawdown)
    end
    println("="^90 * "\n")

    # --- 寫入檔案 ---
    if !isnothing(output_file_path)
        try
            open(output_file_path, "w") do io
                write(io, "="^90 * "\n")
                write(io, " " ^ 25 * "綜合參數效益比較報告 (Summary Report)\n")
                write(io, "報告生成時間: $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))\n")
                write(io, "="^90 * "\n")
                @printf(io, "%-45s | %12s | %8s | %10s | %12s\n", "參數組合 (Parameters)", "總利潤", "勝率", "總ROI", "最大回撤")
                write(io, "-"^90 * "\n")
                for res in summary_results
                    @printf(io, "%-45s | %12.2f | %7.2f%% | %9.2f%% | %12.2f\n",
                        res.parameters, res.total_profit, res.win_rate, res.overall_roi, res.max_drawdown)
                end
                write(io, "="^90 * "\n")
            end
            @info "綜合報告已儲存至: $(output_file_path)"
        catch e
            @error "寫入綜合報告檔案時發生錯誤: " exception=(e, catch_backtrace())
        end
    end
end

"""
    plot_comparison_curves(csv_path::String, parameter_grid, results_dir::String)

為網格搜尋中的所有參數組合繪製一張包含多條累計利潤曲線的比較圖。
"""
function plot_comparison_curves(csv_path::String, parameter_grid, results_dir::String)
    !isfile(csv_path) && (@error "CSV 檔案不存在: $csv_path"; return)
    df = CSV.read(csv_path, DataFrame)
    
    comparison_plot = plot(title="策略比較：累計利潤曲線", xlabel="評估期號", ylabel="累計利潤", legend=:outertopright)

    for params in parameter_grid
        filtered_df = copy(df)
        for (param, value) in pairs(params)
            !hasproperty(filtered_df, param) && continue
            filter!(row -> !isnothing(row[param]) && row[param] == value, filtered_df)
        end

        isempty(filtered_df) && continue
        sort!(filtered_df, :evaluation_period)
        
        cumulative_profit = cumsum(filtered_df.total_profit)
        param_label = join(["$k=$v" for (k,v) in pairs(params)], ", ")
        plot!(comparison_plot, filtered_df.evaluation_period, cumulative_profit, label=param_label, linewidth=2)
    end

    plot_path = joinpath(results_dir, "comparison_cumulative_profit.png")
    savefig(comparison_plot, plot_path)
    @info "綜合比較圖表已儲存至: $(plot_path)"
end

# --- 結束 BatchAnalysisModule ---

# --- 主執行流程 ---



# 載入外部定義的手動模式
include("manual_patterns.jl")


# export run_backtest, run_analysis, get_default_config, generate_summary_report, plot_comparison_curves

function perform_historical_analysis(config)
    # --- 1. 資料準備 ---
    @info "--- 階段 1: 資料準備 ---"
    gxs_combinations = collect(combinations(1:config.max_number, config.gx_size)) |> (x -> map(SVector{config.gx_size, Int8}, x))
    all_numbers_data = load_lottery_data(config.data_file_path, config.numbers_in_draw)
    gxsprices_data = calculate_gxs_gxprice_parallel(all_numbers_data, gxs_combinations)

    # --- 2. 歷史表現分析 ---
    @info "--- 階段 2: 歷史表現分析 ---"
    K, L = Val(config.wins_pattern_length), Val(config.indicator_pattern_length)
    stats_1day = analyze_triggered_pattern_performance(gxsprices_data, config.analysis_start_period, config.analysis_end_period, config.n_future_draws, config.gx_size, K, L)
    
    # --- 3. 篩選高表現模式 ---
    @info "--- 階段 3: 篩選高表現模式 ---"
    high_perf_stats_1day = filter_and_sort_triggered_stats(stats_1day, 
        min_hit_rate=config.filter_min_hit_rate, max_hit_rate=config.filter_max_hit_rate,
        min_num_instances=config.filter_min_instances, max_num_instances=config.filter_max_instances)

    return (
        all_numbers_data = all_numbers_data,
        gxsprices_data = gxsprices_data,
        high_perf_stats = high_perf_stats_1day,
        all_stats = stats_1day
    )
end

function evaluate_for_target_period(analysis_results, target_period, config)
    @info "\n===== 評估目標期號: $(target_period) ====="
    
    # --- 4. 根據模式查找當期 Gx ---
    @info "--- 階段 4: 查找當期 Gx 組合 ---"
    gxsprices_data = analysis_results.gxsprices_data
    high_perf_stats_1day = analysis_results.high_perf_stats
    all_numbers_data = analysis_results.all_numbers_data

    # 基於統計篩選的模式 (修正: 使用傳入的 config 中的 start_period)
    matched_gxs_auto = find_gxs_for_high_performing_patterns_at_period(gxsprices_data, high_perf_stats_1day, target_period, config.analysis_start_period)
    all_matched_gxs = vcat(collect(values(matched_gxs_auto))...)
    
    @info "在期號 $(target_period) 總共找到 $(length(all_matched_gxs)) 個匹配高表現模式的 Gx 組合。"
    println("匹配的 Gx 組合列表:")
    foreach(println, all_matched_gxs)

    # --- 5. 結果儲存與評估 ---
    @info "--- 階段 5: 結果儲存與評估 ---"
    if config.save_results
        !isdir(config.results_dir) && mkdir(config.results_dir)
        hit_rate_str = "min_$(config.filter_min_hit_rate)_max_$(config.filter_max_hit_rate)"
        instances_str = "min_$(config.filter_min_instances)_max_$(config.filter_max_instances)"
        filename = joinpath(config.results_dir, "$(config.game_name)_gxs_p$(target_period)_hr_$(hit_rate_str)_inst_$(instances_str).jls")
        serialize(filename, all_matched_gxs)
        @info "結果已儲存至: $(filename)"
    end

    if !isempty(all_matched_gxs) && (target_period + config.n_future_draws) <= length(all_numbers_data)
        future_draws = [all_numbers_data[target_period + i] for i in 1:config.n_future_draws]
        
        # 計算所有未來期數的總中獎號碼
        future_wins = [intersect.(all_matched_gxs, [draw]) for draw in future_draws]
        total_wins = sum(sum(length.(wins); init=0) for wins in future_wins)
        
        num_invest = length(all_matched_gxs)
        cost = round(num_invest * config.gx_size * 30.4 * config.n_future_draws, digits=2)
        revenue = round(total_wins * 212, digits=2)
        profit = round(revenue - cost, digits=2)
        profit_ratio = cost > 0 ? round(profit / cost, digits=4) : 0.0
        
        println("\n--- 投資效益評估 (未來 $(config.n_future_draws) 期) ---")
        println("投資組合數量: $(num_invest)")
        println("總成本: $(cost)")
        println("總回報: $(revenue)")
        println("總利潤: $(profit)")
        println("利潤率: $(profit_ratio)")
        println("未來 $(config.n_future_draws) 期的開獎號碼: $(future_draws)")  # 新增
        
        # --- 自動記錄日誌 ---
        if config.save_results # 重複使用此旗標來控制日誌記錄
            # 建立一個臨時的 config 來記錄，包含當前的 target_period
            current_config = merge(NamedTuple(config), (target_period_to_find_gxs = target_period,))

            # 準備傳遞給日誌函式的結果
            eval_results = (
                next_draw = reduce(vcat, future_draws), # 合併 future_draws 以保持結構一致
                num_invest = num_invest,
                cost = cost,
                revenue = revenue,
                profit = profit,
                profit_ratio = profit_ratio
            )

            # 新的 CSV 日誌記錄
            csv_log_path = joinpath(config.results_dir, "evaluation_log.csv")
            log_evaluation_to_csv(csv_log_path, current_config, eval_results)

        end
    end
    return all_matched_gxs
end

"""
    run_backtest(config, target_periods_to_evaluate)

執行完整的回測流程。
"""
function run_backtest(config, target_periods_to_evaluate)
    @info "--- 開始回測 ---"
    println("使用參數:")
    for (k, v) in pairs(config); println("  - $k: $v"); end
    println("-"^20)

    # 1. 執行一次耗時的分析，並將結果儲存起來
    analysis_results = perform_historical_analysis(config)
    
    @info "\n\n===== 開始對多個目標期數進行批量評估 ====="
    # 3. 迴圈調用評估函式，這一步會非常快
    for period in target_periods_to_evaluate
        evaluate_for_target_period(analysis_results, period, config)
    end
    @info "--- 回測結束 ---"
end

"""
    run_analysis(config; filter_params...)

對指定的 CSV 日誌執行批次分析。
"""
function run_analysis(config; filter_params...)
    csv_path = joinpath(config.results_dir, "evaluation_log.csv")
    
    @info "--- 執行批次分析 #1 ---"
    analyze_batch_performance(csv_path, 
        do_plot=true,
        results_dir=config.results_dir;
        filter_params...
    )
end

"""
    find_hit_rate_for_specific_pattern(target_wins_pattern::Vector{Int8})

    根據一個特定的 gx_wins 模式，查找並顯示所有相關模式的歷史 hit_rate。

    此函數會：
    1. 根據目標模式的長度自動調整分析參數。
    2. 執行一次完整的歷史分析以計算所有模式的統計數據。
    3. 篩選出所有包含指定 `target_wins_pattern` 的統計結果。
    4. 以表格形式清晰地展示每個完整模式的 hit_rate、觸發次數等資訊。
"""
function find_hit_rate_for_specific_pattern(target_wins_pattern::Vector{Int8})
    @info "--- 開始為特定 gx_wins 模式查找 hit_rate ---"
    
    # 1. 根據目標模式自動設定 config
    config = get_default_config()
    config = merge(config, (
        wins_pattern_length = length(target_wins_pattern),
        n_future_draws = 1, # 根據您的要求，固定 n_future_draws=1
        # 暫時禁用過濾，以獲取所有統計數據
        filter_min_hit_rate = 0.0,
        filter_min_instances = 0
    ))
    @info "分析參數已更新: wins_pattern_length=$(config.wins_pattern_length), n_future_draws=$(config.n_future_draws)"

    # 2. 執行歷史分析以獲取所有統計數據
    # perform_historical_analysis 會返回一個包含 all_stats 的 NamedTuple
    analysis_results = perform_historical_analysis(config)
    all_stats = analysis_results.all_stats
    @info "歷史分析完成，共找到 $(length(all_stats)) 種獨特模式。"

    # 3. 篩選出包含目標 gx_wins_pattern 的所有統計數據
    target_svector_pattern = SVector{config.wins_pattern_length, Int8}(target_wins_pattern)
    found_stats = filter(s -> s.gx_wins_pattern == target_svector_pattern, all_stats)
    
    # 4. 顯示結果
    if isempty(found_stats)
        @warn "在歷史數據中找不到與 gx_wins_pattern = $(target_wins_pattern) 相關的任何紀錄。"
    else
        println("\n" * "="^95)
        println("查詢結果: gx_wins_pattern = $(target_wins_pattern), n_future_draws = 1")
        println("-"^95)
        @printf("%-12s | %-12s | %-12s | %-20s | %-20s\n", "Hit Rate", "總未來中獎", "觸發次數", "cum7_pattern", "cum14_pattern/cum21_pattern")
        println("-"^95)
        
        # 為了更好的可讀性，對結果進行排序
        sort!(found_stats, by = x -> x.hit_rate, rev=true)

        for stat in found_stats
            @printf("%.4f       | %-12d | %-12d | %-20s | %-20s\n", 
                stat.hit_rate, 
                stat.total_future_wins, 
                stat.num_trigger_instances,
                string(collect(stat.cum7_pattern)),
                string(collect(stat.cum14_pattern)) # 僅顯示部分，避免過長
            )
        end
        println("="^95)
        @info "說明：hit_rate 是針對一個完整的模式組合（包含 gx_wins 和 cum 指標）計算的。上述列表顯示了所有包含您指定的 gx_wins_pattern 的完整模式及其各自的 hit_rate。"
    end
    return found_stats
end

"""
    calculate_next_period_hit_rate_for_pattern(gxsprices, target_wins_pattern, gx_size, n_future_draws=1)

    當指定的 `gx_wins_pattern` 出現時，計算其後 `n_future_draws` 期的「總出球率」。
    這個計算是獨立的，它會遍歷所有數據，只根據 `gx_wins_pattern` 進行匹配，
    而不考慮 `cum` 指標的模式。

    # Arguments
    - `gxsprices`: 包含所有 Gx 組合價格數據的字典。
    - `target_wins_pattern`: 要查找的特定 `gx_wins` 模式向量。
    - `gx_size`: Gx 組合的大小 (例如 3)。
    - `n_future_draws`: 模式出現後，要計算未來幾期的中獎球數 (預設為 1)。

    # Returns
    - 一個 NamedTuple，包含 `hit_rate`, `total_future_wins`, `total_occurrences`。
"""
function calculate_next_period_hit_rate_for_pattern(
    gxsprices::Dict{SVector{M, Int8}, GxPrice}, 
    target_wins_pattern::Vector{Int8},
    gx_size::Int,
    n_future_draws::Int = 1
) where M
    @info "--- 為特定 gx_wins_pattern 計算下一期總出球率 ---"
    @info "目標模式: $(target_wins_pattern), 未來期數: $(n_future_draws)"

    K = length(target_wins_pattern)
    target_svector_pattern = SVector{K, Int8}(target_wins_pattern)

    total_future_wins = 0
    total_occurrences = 0
    
    for price_data in values(gxsprices)
        gx_wins_vector = price_data.gx_wins
        len_gx_wins = length(gx_wins_vector)
        
        for i in K:(len_gx_wins - n_future_draws)
            current_pattern = SVector{K, Int8}(view(gx_wins_vector, i-K+1:i))
            if current_pattern == target_svector_pattern
                total_occurrences += 1
                future_wins_slice = view(gx_wins_vector, i+1 : i+n_future_draws)
                total_future_wins += sum(future_wins_slice)
            end
        end
    end

    total_occurrences == 0 && (@warn "在歷史數據中未找到模式 $(target_wins_pattern)。"; return (hit_rate=0.0, total_future_wins=0, total_occurrences=0))

    denominator = gx_size * n_future_draws * total_occurrences
    hit_rate = denominator > 0 ? Float64(total_future_wins) / denominator : 0.0

    @info "計算完成。"
    println("\n" * "="^60, "\n查詢結果: gx_wins_pattern = $(target_wins_pattern)\n", "-"^60)
    @printf("總觸發次數 (Total Occurrences) : %d\n", total_occurrences)
    @printf("總未來中獎球數 (Total Future Wins): %d\n", total_future_wins)
    @printf("總出球率 (Hit Rate)             : %.4f\n", hit_rate)
    println("="^60, "\n說明：總出球率 = 總未來中獎球數 / (Gx球數 * 未來期數 * 總觸發次數)")

    return (hit_rate=hit_rate, total_future_wins=total_future_wins, total_occurrences=total_occurrences)
end

"""
    find_gxs_with_specific_wins_pattern_at_period(gxsprices, target_wins_pattern, target_period, start_period)

    在指定的 `target_period`，查找所有 `gx_wins` 歷史序列符合 `target_wins_pattern` 的 Gx 組合。

    # Arguments
    - `gxsprices`: 包含所有 Gx 組合價格數據的字典。
    - `target_wins_pattern`: 要查找的特定 `gx_wins` 模式向量。
    - `target_period`: 要檢查的目標期號。
    - `start_period`: 數據分析的起始期號，用於計算正確的索引。

    # Returns
    - `Vector{SVector{M, Int8}}`: 符合條件的 Gx 組合列表。
"""
function find_gxs_with_specific_wins_pattern_at_period(
    gxsprices::Dict{SVector{M, Int8}, GxPrice},
    target_wins_pattern::Vector{Int8},
    target_period::Int,
    start_period::Int
) where M
    @info "--- 在期號 $(target_period) 查找匹配特定 gx_wins 模式的 Gx 組合 ---"
    @info "目標模式: $(target_wins_pattern)"

    K = length(target_wins_pattern)
    target_svector_pattern = SVector{K, Int8}(target_wins_pattern)
    
    idx_in_data = target_period - start_period + 1
    
    if idx_in_data < K
        @warn "目標期號 $(target_period) 過早，無法形成長度為 $(K) 的模式。"
        return SVector{M, Int8}[]
    end

    matching_gxs = SVector{M, Int8}[]

    for (gx, price_data) in gxsprices
        gx_wins_vector = price_data.gx_wins
        
        if length(gx_wins_vector) >= idx_in_data
            current_pattern = SVector{K, Int8}(view(gx_wins_vector, idx_in_data - K + 1 : idx_in_data))
            if current_pattern == target_svector_pattern
                push!(matching_gxs, gx)
            end
        end
    end

    @info "查找完成。"
    if isempty(matching_gxs)
        @warn "在期號 $(target_period) 未找到任何匹配模式 $(target_wins_pattern) 的 Gx 組合。"
    else
        println("\n" * "="^60)
        println("在期號 $(target_period) 找到 $(length(matching_gxs)) 個匹配模式的 Gx 組合:")
        println("-"^60)
        for gx in matching_gxs
            println(gx)
        end
        println("="^60)
    end

    return matching_gxs
end

"""
    find_top_hit_rate_patterns(gxsprices, pattern_length, gx_size, n_future_draws, top_n=10)

計算所有可能的 gx_wins_pattern 的出球率，並返回出球率最高的前 N 個模式。

# Arguments
- `gxsprices`: 包含所有 Gx 組合價格數據的字典。
- `pattern_length`: 要分析的 gx_wins 模式長度。
- `gx_size`: Gx 組合的大小。
- `n_future_draws`: 要預測的未來期數。
- `top_n`: 返回的頂部模式數量。

# Returns
- 包含 (pattern, hit_rate, occurrences, wins) 的排序數組。
"""
function find_top_hit_rate_patterns(
    gxsprices::Dict{SVector{M, Int8}, GxPrice},
    pattern_length::Int,
    gx_size::Int,
    n_future_draws::Int,
    top_n::Int=10;
    min_occurrences::Int=5
) where M
    @info "--- 計算所有長度為 $(pattern_length) 的 gx_wins 模式的出球率 ---"
    
    # 收集所有出現的模式
    all_patterns = Dict{Vector{Int8}, Tuple{Int, Int, Float64}}()  # pattern => (wins, occurrences, hit_rate)
    
    for (_, price_data) in gxsprices
        gx_wins = price_data.gx_wins
        
        # 對每個可能的起始位置
        for i in 1:(length(gx_wins) - pattern_length - n_future_draws + 1)
            # 提取當前模式
            current_pattern = gx_wins[i:(i+pattern_length-1)]
            
            # 計算未來中獎情況
            future_win = sum(gx_wins[(i+pattern_length):(i+pattern_length+n_future_draws-1)])
            
            # 更新統計
            if haskey(all_patterns, current_pattern)
                wins, occurrences, _ = all_patterns[current_pattern]
                all_patterns[current_pattern] = (wins + future_win, occurrences + 1, 0.0)
            else
                all_patterns[current_pattern] = (future_win, 1, 0.0)
            end
        end
    end
    
    # 計算每個模式的出球率並過濾低頻模式
    filtered_patterns = []
    for (pattern, (wins, occurrences, _)) in all_patterns
        if occurrences >= min_occurrences
            hit_rate = wins / (gx_size * n_future_draws * occurrences)
            push!(filtered_patterns, (pattern=pattern, hit_rate=hit_rate, occurrences=occurrences, wins=wins))
        end
    end
    
    # 按出球率排序
    sort!(filtered_patterns, by = x -> x.hit_rate, rev=true)
    
    # 返回前 top_n 個
    result = filtered_patterns[1:min(top_n, length(filtered_patterns))]
    
    # 打印結果
    println("\n============================================================")
    println("出球率最高的前 $(length(result)) 個 gx_wins 模式 (最小出現次數: $(min_occurrences)):")
    println("------------------------------------------------------------")
    println("排名 | 模式                                | 出球率  | 出現次數 | 中獎數")
    println("------------------------------------------------------------")
    for (i, r) in enumerate(result)
        @printf("%-4d | %-35s | %.4f | %-8d | %-6d\n", 
                i, string(r.pattern), r.hit_rate, r.occurrences, r.wins)
    end
    println("============================================================")
    
    return result
end

# --- 為了保持腳本可獨立執行，定義 main 函式 ---
function main(; use_example::Int=4)
    if use_example == 1
        # --- 使用範例 1: 執行標準回測與分析 ---
        # 說明：這會執行一個完整的端到端流程，適合評估一組參數在多個期數上的表現。
        @info "--- 執行範例 1: 標準回測與分析 ---"
        config = get_default_config()
        target_periods = collect(1129:1158)
        run_backtest(config, target_periods)
        run_analysis(config; 
            filter_min_hit_rate=config.filter_min_hit_rate, 
            filter_min_instances=config.filter_min_instances
        )
    elseif use_example == 2
        # --- 使用範例 2: 查詢特定 gx_wins_pattern 的 hit_rate ---
        # 說明：這會針對您提供的特定模式，找出所有相關的歷史統計數據。
        @info "--- 執行範例 2: 查詢特定 gx_wins_pattern 的 hit_rate ---"
        target_pattern_to_find = Int8[0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 1] #[0,0,0,1,0,0,1,0,1]
        return find_hit_rate_for_specific_pattern(target_pattern_to_find)

    elseif use_example == 3
        # --- 使用範例 3: 計算特定 gx_wins_pattern 的總出球率 ---
        # 說明：這個函數獨立計算，不考慮 cum 指標。它直接計算一個 gx_wins 模式出現後，
        # 在所有情況下，下一期的平均出球率。
        @info "--- 執行範例 3: 計算特定 gx_wins_pattern 的總出球率 ---"
        config = get_default_config()
        @info "正在準備數據以計算總出球率..."
        gxs_combinations = collect(combinations(1:config.max_number, config.gx_size)) |> (x -> map(SVector{config.gx_size, Int8}, x))
        all_numbers_data = load_lottery_data(config.data_file_path, config.numbers_in_draw)
        gxsprices_data = calculate_gxs_gxprice_parallel(all_numbers_data, gxs_combinations)
        @info "數據準備完成。"
        target_pattern_to_calculate = Int8[0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0]
		#[0,0,0,1,0,0,1,0,1]
        return calculate_next_period_hit_rate_for_pattern(gxsprices_data, target_pattern_to_calculate, config.gx_size, config.n_future_draws)

    elseif use_example == 4
        # --- 使用範例 4: 在特定期號查找匹配特定 gx_wins 模式的 Gx 組合 ---
        # 說明: 這個函數會找出在某一個時間點，哪些 Gx 組合的歷史中獎序列正好形成了您指定的模式。
        @info "--- 執行範例 4: 在特定期號查找匹配特定 gx_wins 模式的 Gx 組合 ---"
        config = get_default_config()
        @info "正在準備數據以查找特定模式的 Gx..."
        gxs_combinations = collect(combinations(1:config.max_number, config.gx_size)) |> (x -> map(SVector{config.gx_size, Int8}, x))
        all_numbers_data = load_lottery_data(config.data_file_path, config.numbers_in_draw)
        gxsprices_data = calculate_gxs_gxprice_parallel(all_numbers_data, gxs_combinations)
        @info "數據準備完成。"
        
        target_pattern_to_find_gxs = Int8[0, 0, 0, 1, 0, 1, 0, 0, 1] 
		#[0,0,0,1,0,0,1,0,1]
        target_period_for_pattern = config.target_period_to_find_gxs # 使用 config 中的目標期號
        
        return find_gxs_with_specific_wins_pattern_at_period(
            gxsprices_data, 
            target_pattern_to_find_gxs, 
            target_period_for_pattern, 
            config.analysis_start_period
        )
    elseif use_example == 5
        # --- 使用範例 5: 找出出球率最高的 gx_wins 模式 ---
        @info "--- 執行範例 5: 找出出球率最高的 gx_wins 模式 ---"
        config = get_default_config()
        @info "正在準備數據..."
        gxs_combinations = collect(combinations(1:config.max_number, config.gx_size)) |> (x -> map(SVector{config.gx_size, Int8}, x))
        all_numbers_data = load_lottery_data(config.data_file_path, config.numbers_in_draw)
        gxsprices_data = calculate_gxs_gxprice_parallel(all_numbers_data, gxs_combinations)
        @info "數據準備完成。"
        
        pattern_length = 9  # 可以根據需要調整
        return find_top_hit_rate_patterns(
            gxsprices_data, 
            pattern_length, 
            config.gx_size, 
            config.n_future_draws,
            10,  # 返回前10名
            min_occurrences=5  # 最小出現次數
        )
    else
        @error "無效的使用範例編號: $(use_example)。請選擇 1 到 5。"
    end
end

# 如果此腳本是主程序，則執行 main()
# if abspath(PROGRAM_FILE) == @__FILE__
    # 您可以透過修改 use_example 的值來切換不同的執行功能
    # 1: 執行標準回測與分析
    # 2: 查詢特定 gx_wins_pattern 的 hit_rate
    # 3: 計算特定 gx_wins_pattern 的總出球率
    # 4: 在特定期號查找匹配特定 gx_wins 模式的 Gx 組合
    # 5: 找出出球率最高的 gx_wins 模式
    result = main(use_example=5)
# end








"""
# Lottery Pattern Analysis and Prediction Tool

## 1. 總覽 (Overview)

此 Julia 腳本提供一個全面的框架，用於分析歷史彩票數據。它旨在識別統計上顯著的「觸發模式」，並利用這些模式來預測未來開獎中可能表現優異的數字組合 (Gx)。

該系統包含回測策略、評估投資回報以及生成詳細績效報告的功能。

## 2. 核心功能 (Key Features)

- **模組化架構**: 代碼被組織成邏輯清晰的模組，分別處理數據、分析、預測和日誌記錄。
- **可配置化分析**: 所有參數都集中在 `get_default_config()` 函數中，方便進行調整和實驗。
- **基於模式的預測**: 透過識別歷史中獎次數 (`gx_wins`) 和累積指標 (`cum7`, `cum14`, `cum21`) 的模式來發現觸發事件。
- **回測引擎**: `run_backtest` 函數允許評估策略在一段歷史期間內的表現。
- **績效評估**: 自動計算每個測試週期的關鍵指標，如成本、收入、利潤和投資回報率 (ROI)。
- **自動化日誌**: 評估結果會自動保存到 `results/evaluation_log.csv` 文件中，以便永久存儲和後續分析。
- **批次分析與報告**: `run_analysis` 和 `generate_summary_report` 等函數可以處理日誌文件，以比較不同參數設置的性能，計算匯總 KPI（如總利潤、勝率、最大回撤），並生成摘要表。
- **視覺化**: 能夠為不同策略生成並保存累計利潤曲線圖。

## 3. 如何使用 (How to Use)

### 3.1. 環境準備 (Prerequisites)

1.  **安裝 Julia**: 建議使用 Julia 1.6 或更高版本。
2.  **安裝必要的套件**: 在 Julia REPL 中執行以下命令：
    ```julia
    using Pkg
    Pkg.add(["Combinatorics", "CSV", "DataFrames", "ThreadSafeDicts", "Statistics", "Plots", "StaticArrays", "Printf", "StatsBase", "Dates", "Serialization", "Markdown"])
    ```

### 3.2. 執行步驟 (Execution Steps)

1.  **準備數據**:
    - 將您的彩票數據 CSV 文件（例如 `fan5.csv`）放置在 `data/` 目錄中。
    - CSV 格式應為：`期號,號碼1,號碼2,號碼3,號碼4,號碼5`，且不含標頭 (header)。

2.  **配置與執行**:
    - 腳本的主要執行入口點是檔案末尾的 `main()` 函數。
    - 您可以在 `main()` 函數內部修改 `config` 和 `target_periods` 來進行回測。
    - **`config`**: 使用 `get_default_config()` 獲取預設配置，您可以根據需要覆蓋特定參數。
    - **`target_periods`**: 定義您想要進行回測評估的目標期號範圍。腳本會對這個範圍內的每一期進行預測和評估。

    ```julia
    # 位於檔案末尾的 main 函數示例
    function main()
        # 執行回測
        config = get_default_config() # 可在此處修改 config，例如 config = merge(get_default_config(), (filter_min_hit_rate = 0.3,))
        target_periods = collect(1129:1158) # 設定要回測的期數範圍
        run_backtest(config, target_periods)

        # 執行分析
        run_analysis(config;
            filter_min_hit_rate=config.filter_min_hit_rate,
            filter_min_instances=config.filter_min_instances
        )
    end
    ```

3.  **運行腳本**:
    - 從您的專案根目錄，在終端中執行腳本：
      ```bash
      julia src/analyze_triggered_pattern_performance_standalone.jl
      ```
    - 這將執行 `main()` 函數，首先運行回測，然後對生成的日誌結果進行匯總分析。

## 4. 工作流程說明 (Workflow Explained)

腳本的執行分為兩個主要階段：

1.  **`run_backtest` (回測)**:
    - **歷史分析 (`perform_historical_analysis`)**: 這是計算最密集的部分。它只會運行*一次*，分析 `config` 中設定的整個歷史範圍 (`analysis_start_period` 到 `analysis_end_period`)，找出所有可能的模式及其歷史表現（命中率、出現次數）。
    - **迭代評估 (`evaluate_for_target_period`)**: 接著，它會遍歷 `target_periods` 中的每一個期號。對於每一期，它會檢查哪些預先計算出的「高表現模式」在當前是活躍的，收集對應的 Gx 組合，並根據下一期的實際開獎號碼評估其表現，最後將結果記錄到 `results/evaluation_log.csv`。

2.  **`run_analysis` (分析)**:
    - 讀取 `results/evaluation_log.csv` 文件。
    - 根據提供的參數篩選記錄。
    - 計算並打印一份批次分析報告，包含總利潤、勝率和最大回撤等匯總指標。
    - 生成並保存一張累計利潤圖。

## 5. 輸出檔案說明 (Output Files)

所有輸出檔案都將保存在 `results/` 目錄中：

- **`evaluation_log.csv`**: 主要的日誌文件，記錄了每次回測評估的詳細參數和結果。
- **`*.jls`**: 序列化的 Julia 物件，包含為特定時期預測出的 Gx 組合。
- **`*.png`**: 由批次分析生成的圖表，例如累計利潤曲線圖。

"""


md"""
analyze_triggered_pattern_performance_standalone.jl 確實是一個非常優秀的腳本，它提供了更加完整和系統化的功能：
完整的配置系統：使用 get_default_config() 提供統一的配置管理
全面的分析流程：從數據載入到模式分析、評估和結果輸出的完整流程
回測系統：內建回測功能，可評估預測效果
批量分析：支持對多個目標期數進行批量評估
核心算法解析

這個腳本的核心算法更加精煉和高效：

模式累積器：使用 _accumulate_pattern_stats 高效收集模式統計
統計生成：_create_stats_from_accumulators 將原始數據轉換為有意義的統計結果
模式觸發分析：專注於分析「觸發模式」的表現，這是一種更精確的預測方法
參數化模式長度：使用 Val{K} 和 Val{L} 在編譯時確定模式長度，提高效能
與 A0031 的比較

相比 A0031， analyze_triggered_pattern_performance_standalone.jl 確實有許多優勢：

更完善的架構：模組化設計和完整的工作流
更精確的分析方法：專注於觸發模式的表現分析
更完善的功能：包含配置系統、回測系統和批量分析
更好的可擴展性：清晰的代碼結構使其更易於擴展
更多的版本迭代：顯示出持續的改進和優化
這個腳本代表了專案中最先進的分析方法，是整個專案的核心價值所在。它不僅提供了強大的分析能力，還具有良好的用戶體驗和可擴展性，是專案中最有價值的組件。

思考步驟：

分析兩腳本功能差異
識別A0031獨特功能
評估整合可行性
設計整合方案
提出具體代碼建議
A0031 與 analyze_triggered_pattern_performance_standalone.jl 整合方案
功能差異分析

A0031 獨特功能
超級複合模式：同時考慮四種指標 (gx_wins, cum7, cum14, cum21)
多期預測：支持預測未來多期的中獎情況
特定組合分析：支持 part13sidx 特定組合的分析
投資回報計算：包含 car profit 和 star2 profit 計算
模式篩選：更靈活的模式篩選機制
整合方案設計

整合後的功能優勢
整合後的腳本將結合兩者的優點：

保留 analyze_triggered_pattern_performance 的優勢：
模組化設計
完整的配置系統
回測和批量分析功能
高效的模式觸發分析
增加 A0031 的獨特功能：
超級複合模式分析
多期預測支持
part13sidx 特定組合分析
投資回報計算
更靈活的模式篩選
新增的整合優勢：
統一的配置系統，支持兩種分析模式
更全面的分析選項
更豐富的結果輸出
更強大的投資決策支持
整合建議
分階段實施：
第一階段：增加超級複合模式分析
第二階段：增加投資回報計算
第三階段：增加 part13sidx 支持
第四階段：整合配置系統
保持向後兼容：
使用配置選項控制是否啟用新功能
保留原有的函數接口
提供遷移指南
增加測試：
為新增功能編寫單元測試
進行回測比較，確保結果一致性
性能測試，確保整合後不會降低效率
通過這種整合方式，可以將 A0031 的獨特功能無縫融入到 analyze_triggered_pattern_performance_standalone.jl 中，創建一個更強大、更全面的彩票分析工具。

"""

# TODO: 改進清單
"""
# 改進清單

## 效能優化
- [ ] 實現 CPU 加速計算，特別是對於大量組合的處理
- [ ] 優化 `_accumulate_pattern_stats` 函數，減少重複計算
- [ ] 實現增量更新機制，只處理新增的數據
- [ ] 使用更高效的數據結構，如 SortedDict 來加速查找

## 功能擴展
- [ ] 增加更多模式類型，如價格變化率、波動性等
- [ ] 實現自適應模式長度，根據數據特性自動調整
- [ ] 增加機器學習模型，如 LSTM 或 Transformer 來捕捉更複雜的模式
- [ ] 開發交互式視覺化界面，展示模式觸發和預測結果

## 代碼重構
- [ ] 將配置系統改為使用結構體，提供更好的類型安全
- [ ] 增加更多單元測試，確保各模組正確性
- [ ] 改進錯誤處理機制，增加日誌系統
- [ ] 增加詳細文檔，包括每個函數的用途和參數說明

## 新功能
- [ ] 實現模式組合策略，結合多個高性能模式
- [ ] 增加風險評估功能，計算預測的置信區間
- [ ] 開發實時監控系統，自動檢測新的高性能模式
- [ ] 增加模式演化分析，研究模式效果隨時間的變化
- [ ] 實現多彩票遊戲的通用接口，支持不同遊戲的分析

## 用戶體驗
- [ ] 開發命令行界面，方便快速執行常用分析
- [ ] 增加結果匯出功能，支持多種格式（CSV, JSON, Excel）
- [ ] 實現配置文件系統，方便保存和加載不同的分析設置
- [ ] 增加進度報告和估計完成時間功能
"""


# to-do list
# - [x] 如何修改 `analyze_triggered_pattern_performance_standalone.jl` 腳本，讓它在每次評估後自動將結果寫入這個日誌文件？
# - [x] 如何修改日誌記錄功能，讓「結論與備註」可以根據利潤率自動生成初步的文字？
# - [x] 請幫我修改 `evaluate_for_target_period` 函式，使其能夠根據 `CONFIG.n_future_draws` 的值來正確評估多期的獲利。
# - [ ] 請幫我修改 `log_evaluation_to_markdown` 函式，讓它在日誌中能更清晰地展示多期評估的細節，例如每期的開獎號碼和對應的獲利情況。
# - [x] 在 `CONFIG` 中，`gx_wins_lookback` 和 `pattern_lookback` 這些參數的命名有點不直觀，可以幫我重構一下，讓它們的意義更清晰嗎？同時也請更新所有使用到它們的地方。
# - [ ] 增加一個功能，將多個最佳參數組合生成的 Gx 建議進行合併和去重，並根據其在不同最佳參數集中的出現頻率或加權得分進行排序，提供一個最終的、精簡的投資列表。
# 階段四項目1撰寫自動化執行器 (Runner Script)
