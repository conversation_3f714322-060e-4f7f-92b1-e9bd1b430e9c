#!/usr/bin/env julia
# A0032_參數最佳化_獲利評估.jl
# 評估不同參數組合在特定期間的獲利表現

using Combinatorics, Statistics, Printf, Markdown

"""
    evaluate_profit_for_parameters(
        game_name::String,
        target_period_range::UnitRange{Int},
        gx_size_options::Vector{Int},
        hit_rate_options::Vector{Float64},
        num_instances_options::Vector{Int}
    )

評估不同參數組合在指定期間的獲利表現。

# 參數
- `game_name::String`: 遊戲名稱，例如 "fan5" 或 "tw539"
- `target_period_range::UnitRange{Int}`: 要評估的期數範圍，例如 1122:1131
- `gx_size_options::Vector{Int}`: 要測試的 Gx 組合大小選項
- `hit_rate_options::Vector{Float64}`: 要測試的最小命中率選項
- `num_instances_options::Vector{Int}`: 要測試的最小實例數選項

# 返回
- `DataFrame`: 包含所有參數組合及其獲利表現的數據框
"""
function evaluate_profit_for_parameters(
    game_name::String,
    target_period_range::UnitRange{Int},
    gx_size_options::Vector{Int},
    hit_rate_options::Vector{Float64},
    num_instances_options::Vector{Int}
)
    # 初始化結果存儲
    results = []
  
    # 設定固定參數
    local_param_numbers_per_draw = 5
    local_param_max_number = 39
    local_param_n_future_draws = 1
    local_param_data_start_abs_idx = 1
    local_param_condition_check_end_abs_idx = maximum(target_period_range)

    # 載入必要的函數和數據
    # 注意：這裡假設已經有相關函數可用，實際使用時需要確保這些函數已定義
    all_numbers_data = load_lottery_data("data/$(game_name).csv", local_param_numbers_per_draw)
    
    # 遍歷所有參數組合
    for gx_size in gx_size_options
        for hit_rate in hit_rate_options
            for num_instances in num_instances_options
                # 遍歷目標期數範圍
                for target_idx in target_period_range
                    # 記錄當前參數組合
                    param_set = (
                        target_idx = target_idx,
                        gx_size = gx_size,
                        hit_rate = hit_rate,
                        num_instances = num_instances
                    )
                    
                    # 執行分析並計算獲利
                    metrics = calculate_profit_for_params(
                        all_numbers_data,
                        target_idx,
                        gx_size,
                        hit_rate,
                        num_instances,
                        local_param_max_number,
                        local_param_n_future_draws,
                        local_param_data_start_abs_idx,
                        local_param_condition_check_end_abs_idx
                    )
                    
                    # 儲存結果
                    push!(results, (param_set..., metrics...))
                    
                    # 輸出進度
                    @printf "評估參數: 期數=%d, gx_size=%d, hit_rate=%.3f, num_instances=%d, 獲利=%.2f, 獲利率=%.2f%%\n" target_idx gx_size hit_rate num_instances metrics.profit (metrics.profit_ratio * 100)
                end
            end
        end
    end
    
    # 將結果轉換為DataFrame並返回
    return results
end

"""
    calculate_profit_for_params(
        all_numbers_data,
        target_idx,
        gx_size,
        hit_rate,
        num_instances,
        max_number,
        n_future_draws,
        data_start_abs_idx,
        condition_check_end_abs_idx
    )

使用指定參數計算特定期數的獲利。

# 返回
- `NamedTuple`: 包含獲利、成本和獲利率 `(profit, cost, profit_ratio)`
"""
function calculate_profit_for_params(
    all_numbers_data,
    target_idx,
    gx_size,
    hit_rate,
    num_instances,
    max_number,
    n_future_draws,
    data_start_abs_idx,
    condition_check_end_abs_idx
)
    # 計算所有可能的組合
    gxs_combinations = collect(combinations(1:max_number, gx_size)) |> (x -> map(SVector{gx_size, Int8}, x))

    # 計算 GxPrice 數據
    data_for_gxp_calc = all_numbers_data[1:condition_check_end_abs_idx]
    gxsprices_data = calculate_gxs_gxprice_parallel(data_for_gxp_calc, gxs_combinations)
    
    # 分析觸發模式表現
    triggered_stats_results = analyze_triggered_pattern_performance(
        gxsprices_data,
        data_start_abs_idx,
        condition_check_end_abs_idx,
        n_future_draws,
        gx_size
    )

    # 過濾和排序結果
    filtered_stats = filter_and_sort_triggered_stats(
        triggered_stats_results,
        sort_by = :hit_rate,
        sort_rev = true,
        min_hit_rate = hit_rate,
        min_num_instances = num_instances,
        gx_wins_pattern = nothing
    ) 

    # 查找符合條件的 Gx 組合
    found_gxs_map = find_gxs_for_high_performing_patterns_at_period(
        gxsprices_data,
        filtered_stats,
        target_idx,
        data_start_abs_idx
    )

    # 計算獲利
    # 1. 獲取下一期的實際開獎號碼
    next_draw_idx = target_idx + n_future_draws # 根據提供的邏輯，未來期數應為 n_future_draws
    if next_draw_idx > length(all_numbers_data)
        return (profit = 0.0, cost = 0.0, profit_ratio = 0.0)  # 如果沒有下一期數據，返回0
    end

    next_draw_numbers = all_numbers_data[next_draw_idx]

    # 2. 扁平化所有推薦的 Gx 組合
    filterd_gxs = SVector{gx_size, Int8}[] # 初始化為正確的類型
    if !isempty(found_gxs_map)
        for gxs_list in values(found_gxs_map)
            append!(filterd_gxs, gxs_list)
        end
    end

    num_invest = length(filterd_gxs)

    if num_invest == 0
        return (profit = 0.0, cost = 0.0, profit_ratio = 0.0) # 避免除以零
    end

    # 計算中獎球數 (num_wins)
    num_wins = 0
    for gx_combo in filterd_gxs
        num_wins += length(intersect(next_draw_numbers, gx_combo))
    end

    # 使用提供的成本和獎金常數
    cost_per_investment = 91.2
    return_per_winning_ball = 212.0

    cost = num_invest * cost_per_investment
    return_money = num_wins * return_per_winning_ball

    profit = return_money - cost
    profit_ratio = cost > 0 ? profit / cost : 0.0
    return (profit = profit, cost = cost, profit_ratio = profit_ratio)
end

"""
    find_best_parameters()

執行參數最佳化，找出獲利最大的參數組合。
"""
function find_best_parameters()
    # 設定評估參數範圍
    game_name = "fan5"
    target_period_range = 1122:1131
    gx_size_options = [3]
    hit_rate_options = [0.14, 0.16, 0.18, 0.20, 0.22]
    num_instances_options = [10] #[10,20,30] #[10, 50, 100, 200]
    
    println("開始評估參數組合在期間 $(target_period_range) 的獲利表現...")
    
    # 評估所有參數組合
    results = evaluate_profit_for_parameters(
        game_name,
        target_period_range,
        gx_size_options,
        hit_rate_options,
        num_instances_options
    )
    
    # 找出獲利最大的參數組合
    sort!(results, by = r -> r.profit_ratio, rev = true)
    
    println("\n===== 獲利率最大的前10組參數 =====")
    for (i, result) in enumerate(results[1:min(10, length(results))])
        @printf "%2d. 期數=%d, gx_size=%d, hit_rate=%.3f, num_instances=%d, 獲利率=%.2f%% (獲利=%.2f, 成本=%.2f)\n" i result.target_idx result.gx_size result.hit_rate result.num_instances (result.profit_ratio * 100) result.profit result.cost
    end
    
    # 按期數分組，找出每期最佳參數
    println("\n===== 各期最佳參數 =====")
    for period in target_period_range
        period_results = filter(r -> r.target_idx == period, results)
        if !isempty(period_results)
            best = first(period_results)
            @printf "期數 %d: gx_size=%d, hit_rate=%.3f, num_instances=%d, 獲利率=%.2f%% (獲利=%.2f, 成本=%.2f)\n" period best.gx_size best.hit_rate best.num_instances (best.profit_ratio * 100) best.profit best.cost
        end
    end
    
    # 計算每個參數的平均表現
    println("\n===== 參數平均表現 =====")
    
    # gx_size 平均表現
    println("GX 組合大小平均表現:")
    for gx_size in gx_size_options
        gx_results = filter(r -> r.gx_size == gx_size, results)
        avg_profit_ratio = mean([r.profit_ratio for r in gx_results])
        @printf "gx_size=%d: 平均獲利率=%.2f%%\n" gx_size (avg_profit_ratio * 100)
    end
    
    # hit_rate 平均表現
    println("\n命中率閾值平均表現:")
    for hit_rate in hit_rate_options
        hr_results = filter(r -> r.hit_rate == hit_rate, results)
        avg_profit_ratio = mean([r.profit_ratio for r in hr_results])
        @printf "hit_rate=%.3f: 平均獲利率=%.2f%%\n" hit_rate (avg_profit_ratio * 100)
    end
    
    # num_instances 平均表現
    println("\n最小實例數平均表現:")
    for num_instances in num_instances_options
        ni_results = filter(r -> r.num_instances == num_instances, results)
        avg_profit_ratio = mean([r.profit_ratio for r in ni_results])
        @printf "num_instances=%d: 平均獲利率=%.2f%%\n" num_instances (avg_profit_ratio * 100)
    end
    
    return results
end

# 執行主函數
# if abspath(PROGRAM_FILE) == @__FILE__
    include("A0015_0007_05_all_pattern_analysis_standalone.jl")
    find_best_parameters()
# end

md"""
評估參數: 期數=1122, gx_size=3, hit_rate=0.160, num_instances=10, 獲利=-6607.20
評估參數: 期數=1123, gx_size=3, hit_rate=0.160, num_instances=10, 獲利=2840.80
評估參數: 期數=1124, gx_size=3, hit_rate=0.160, num_instances=10, 獲利=21920.80
評估參數: 期數=1125, gx_size=3, hit_rate=0.160, num_instances=10, 獲利=13884.00
評估參數: 期數=1126, gx_size=3, hit_rate=0.160, num_instances=10, 獲利=6113.60
評估參數: 期數=1127, gx_size=3, hit_rate=0.160, num_instances=10, 獲利=24299.20
評估參數: 期數=1128, gx_size=3, hit_rate=0.160, num_instances=10, 獲利=29018.40
評估參數: 期數=1129, gx_size=3, hit_rate=0.160, num_instances=10, 獲利=9491.20
評估參數: 期數=1130, gx_size=3, hit_rate=0.160, num_instances=10, 獲利=5270.40
評估參數: 期數=1131, gx_size=3, hit_rate=0.160, num_instances=10, 獲利=8397.60
"""