# 彩票模式分析工具 - 安裝配置指南

## 📋 目錄
1. [系統需求](#系統需求)
2. [<PERSON> 安裝](#julia-安裝)
3. [套件安裝](#套件安裝)
4. [項目設置](#項目設置)
5. [數據準備](#數據準備)
6. [配置調整](#配置調整)
7. [測試安裝](#測試安裝)
8. [常見問題](#常見問題)

## 🖥️ 系統需求

### 最低需求
- **作業系統**: Windows 10/11, macOS 10.14+, Linux (Ubuntu 18.04+)
- **記憶體**: 4GB RAM
- **處理器**: 雙核心 2.0GHz
- **儲存空間**: 2GB 可用空間
- **網路**: 安裝時需要網路連接

### 建議需求
- **記憶體**: 8GB+ RAM
- **處理器**: 四核心 3.0GHz+
- **儲存空間**: SSD 硬碟
- **網路**: 穩定的網路連接

## 🚀 Julia 安裝

### Windows 安裝

#### 方法 1: 官方安裝程式
```bash
# 1. 訪問 https://julialang.org/downloads/
# 2. 下載 Windows x64 版本
# 3. 執行安裝程式
# 4. 選擇 "Add Julia to PATH" 選項
```

#### 方法 2: Chocolatey
```powershell
# 安裝 Chocolatey (如果尚未安裝)
Set-ExecutionPolicy Bypass -Scope Process -Force
iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))

# 安裝 Julia
choco install julia
```

#### 方法 3: Winget
```powershell
winget install julia
```

### macOS 安裝

#### 方法 1: 官方安裝程式
```bash
# 1. 訪問 https://julialang.org/downloads/
# 2. 下載 macOS x64 版本
# 3. 拖拽到 Applications 資料夾
# 4. 添加到 PATH
echo 'export PATH="/Applications/Julia-1.9.app/Contents/Resources/julia/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

#### 方法 2: Homebrew
```bash
# 安裝 Homebrew (如果尚未安裝)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安裝 Julia
brew install julia
```

### Linux 安裝

#### Ubuntu/Debian
```bash
# 方法 1: 官方二進制文件
wget https://julialang-s3.julialang.org/bin/linux/x64/1.9/julia-1.9.4-linux-x86_64.tar.gz
tar -xzf julia-1.9.4-linux-x86_64.tar.gz
sudo mv julia-1.9.4 /opt/
sudo ln -s /opt/julia-1.9.4/bin/julia /usr/local/bin/julia

# 方法 2: 套件管理器
sudo apt update
sudo apt install julia
```

#### CentOS/RHEL/Fedora
```bash
# Fedora
sudo dnf install julia

# CentOS/RHEL (需要 EPEL)
sudo yum install epel-release
sudo yum install julia
```

### 驗證安裝
```bash
# 檢查 Julia 版本
julia --version

# 應該顯示類似：julia version 1.9.4
```

## 📦 套件安裝

### 啟動 Julia REPL
```bash
julia
```

### 安裝必要套件
```julia
# 進入套件管理模式
]

# 安裝必要套件
add CSV DataFrames StaticArrays Combinatorics Dates Printf

# 退出套件管理模式
# 按 Backspace 或 Ctrl+C

# 驗證套件安裝
using CSV, DataFrames, StaticArrays, Combinatorics, Dates, Printf
println("所有套件安裝成功！")

# 退出 Julia
exit()
```

### 批量安裝腳本
創建 `install_packages.jl` 文件：
```julia
# install_packages.jl
using Pkg

packages = [
    "CSV",
    "DataFrames", 
    "StaticArrays",
    "Combinatorics",
    "Dates",
    "Printf"
]

println("開始安裝套件...")
for pkg in packages
    println("安裝 $pkg...")
    Pkg.add(pkg)
end

println("驗證套件安裝...")
for pkg in packages
    try
        eval(Meta.parse("using $pkg"))
        println("✓ $pkg 安裝成功")
    catch e
        println("❌ $pkg 安裝失敗: $e")
    end
end

println("套件安裝完成！")
```

執行安裝腳本：
```bash
julia install_packages.jl
```

## 📁 項目設置

### 創建項目目錄結構
```bash
# Windows
mkdir StandAlone
cd StandAlone
mkdir src data results

# macOS/Linux
mkdir -p StandAlone/{src,data,results}
cd StandAlone
```

### 下載主程序文件
將 `analyze_triggered_pattern_performance_standalone_流式處理架構.jl` 放置到 `src/` 目錄中。

### 目錄結構確認
```
StandAlone/
├── src/
│   ├── analyze_triggered_pattern_performance_standalone_流式處理架構.jl
│   └── (其他 Julia 文件)
├── data/
│   ├── fan5.csv
│   └── (其他數據文件)
├── results/
│   └── (分析結果將保存在這裡)
├── 使用者手冊.md
├── 快速參考.md
├── 範例腳本.jl
└── 安裝配置指南.md
```

## 📊 數據準備

### 數據格式要求
CSV 文件格式，無標題行：
```csv
20240101,01,15,23,31,39
20240102,03,12,18,25,37
20240103,07,14,22,28,35
...
```

### 數據格式說明
- **第一列**: 日期 (格式: YYYYMMDD)
- **第二列到第六列**: 開獎號碼 (1-39)
- **分隔符**: 逗號
- **編碼**: UTF-8
- **無標題行**

### 數據驗證腳本
創建 `validate_data.jl`：
```julia
using CSV, DataFrames

function validate_data_file(file_path::String)
    println("驗證數據文件: $file_path")
    
    if !isfile(file_path)
        println("❌ 文件不存在")
        return false
    end
    
    try
        data = CSV.read(file_path, DataFrame, header=false)
        
        # 檢查欄位數量
        if ncol(data) != 6
            println("❌ 欄位數量錯誤，應為 6 欄")
            return false
        end
        
        # 檢查號碼範圍
        numbers = Matrix(data[:, 2:6])
        if any(numbers .< 1) || any(numbers .> 39)
            println("❌ 號碼範圍錯誤，應在 1-39 之間")
            return false
        end
        
        # 檢查重複號碼
        for i in 1:nrow(data)
            row_numbers = collect(data[i, 2:6])
            if length(unique(row_numbers)) != 5
                println("❌ 第 $i 行有重複號碼")
                return false
            end
        end
        
        println("✓ 數據文件驗證通過")
        println("  總記錄數: $(nrow(data))")
        return true
        
    catch e
        println("❌ 文件格式錯誤: $e")
        return false
    end
end

# 驗證數據文件
validate_data_file("data/fan5.csv")
```

### 數據轉換工具
如果您的數據格式不同，可以使用以下腳本轉換：

```julia
# convert_data.jl
using CSV, DataFrames, Dates

function convert_excel_to_csv(input_file::String, output_file::String)
    println("轉換 $input_file 到 $output_file")
    
    # 讀取 Excel 文件 (需要安裝 XLSX.jl)
    # using XLSX
    # data = XLSX.readtable(input_file, "Sheet1")
    
    # 或讀取有標題的 CSV
    data = CSV.read(input_file, DataFrame)
    
    # 假設原始格式有標題行
    # 選擇需要的欄位並重新排列
    converted_data = select(data, [:date, :n1, :n2, :n3, :n4, :n5])
    
    # 轉換日期格式
    converted_data.date = Dates.format.(Date.(converted_data.date), "yyyymmdd")
    
    # 保存為無標題 CSV
    CSV.write(output_file, converted_data, header=false)
    
    println("✓ 轉換完成")
end
```

## ⚙️ 配置調整

### 自定義配置文件
創建 `config.jl`：
```julia
# config.jl - 自定義配置

"""
獲取自定義配置
根據您的彩票類型調整參數
"""
function get_custom_config()
    return (
        # 數據文件路徑
        data_file_path = "data/fan5.csv",
        
        # 彩票參數
        max_number = 39,                    # 號碼範圍 1-39
        numbers_in_draw = 5,                # 每期開獎 5 個號碼
        
        # 分析參數
        gx_size = 3,                        # Gx 組合大小 (2 或 3)
        wins_pattern_length = 12,           # 模式長度 (8-15)
        n_future_draws = 2,                 # 預測期數 (1-3)
        
        # 輸出設置
        results_dir = "results"
    )
end

# 不同彩票類型的預設配置
function get_config_taiwan_lotto()
    return (
        data_file_path = "data/taiwan_lotto.csv",
        max_number = 49,
        numbers_in_draw = 6,
        gx_size = 3,
        wins_pattern_length = 10,
        n_future_draws = 2,
        results_dir = "results"
    )
end

function get_config_powerball()
    return (
        data_file_path = "data/powerball.csv",
        max_number = 69,
        numbers_in_draw = 5,
        gx_size = 3,
        wins_pattern_length = 12,
        n_future_draws = 2,
        results_dir = "results"
    )
end
```

### 性能調整
創建 `performance_config.jl`：
```julia
# performance_config.jl - 性能優化配置

"""
根據系統性能調整參數
"""
function get_performance_config(system_type::String="medium")
    base_config = (
        data_file_path = "data/fan5.csv",
        max_number = 39,
        numbers_in_draw = 5,
        results_dir = "results"
    )
    
    if system_type == "low"
        # 低性能系統配置
        return merge(base_config, (
            gx_size = 2,
            wins_pattern_length = 8,
            n_future_draws = 1
        ))
    elseif system_type == "high"
        # 高性能系統配置
        return merge(base_config, (
            gx_size = 3,
            wins_pattern_length = 15,
            n_future_draws = 3
        ))
    else
        # 中等性能系統配置
        return merge(base_config, (
            gx_size = 3,
            wins_pattern_length = 12,
            n_future_draws = 2
        ))
    end
end

# 設置 Julia 線程數
function setup_julia_threads(num_threads::Int=4)
    ENV["JULIA_NUM_THREADS"] = string(num_threads)
    println("設置 Julia 線程數為: $num_threads")
    println("重新啟動 Julia 以生效")
end
```

## 🧪 測試安裝

### 基本功能測試
創建 `test_installation.jl`：
```julia
# test_installation.jl - 安裝測試腳本

println("開始測試安裝...")

# 測試 1: 套件載入
println("\n1. 測試套件載入...")
try
    using CSV, DataFrames, StaticArrays, Combinatorics, Dates, Printf
    println("✓ 所有套件載入成功")
catch e
    println("❌ 套件載入失敗: $e")
    exit(1)
end

# 測試 2: 主程序載入
println("\n2. 測試主程序載入...")
try
    include("src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl")
    println("✓ 主程序載入成功")
catch e
    println("❌ 主程序載入失敗: $e")
    exit(1)
end

# 測試 3: 數據文件檢查
println("\n3. 測試數據文件...")
if isfile("data/fan5.csv")
    try
        data = CSV.read("data/fan5.csv", DataFrame, header=false)
        println("✓ 數據文件讀取成功，共 $(nrow(data)) 筆記錄")
    catch e
        println("❌ 數據文件格式錯誤: $e")
    end
else
    println("⚠️  數據文件不存在，請準備 data/fan5.csv")
end

# 測試 4: 配置載入
println("\n4. 測試配置載入...")
try
    config = get_default_config()
    println("✓ 配置載入成功")
    println("  最大號碼: $(config.max_number)")
    println("  Gx 大小: $(config.gx_size)")
    println("  模式長度: $(config.wins_pattern_length)")
catch e
    println("❌ 配置載入失敗: $e")
end

# 測試 5: 結果目錄
println("\n5. 測試結果目錄...")
if isdir("results")
    println("✓ 結果目錄存在")
else
    try
        mkdir("results")
        println("✓ 結果目錄創建成功")
    catch e
        println("❌ 結果目錄創建失敗: $e")
    end
end

println("\n" * "="^50)
println("安裝測試完成！")
println("如果所有測試都通過，您可以開始使用程序了。")
println("運行命令: julia src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl")
```

執行測試：
```bash
julia test_installation.jl
```

### 快速功能測試
```bash
# 啟動程序並測試功能 1
julia src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl

# 在程序中輸入：
# 1 (選擇功能 1)
# 0 (退出程序)
```

## ❓ 常見問題

### Q1: Julia 安裝後找不到命令
**A**: 檢查 PATH 環境變量設置
```bash
# Windows
echo $env:PATH | Select-String julia

# macOS/Linux  
echo $PATH | grep julia

# 如果沒有，手動添加到 PATH
```

### Q2: 套件安裝失敗
**A**: 檢查網路連接和代理設置
```julia
# 設置代理 (如果需要)
ENV["HTTP_PROXY"] = "http://proxy.company.com:8080"
ENV["HTTPS_PROXY"] = "http://proxy.company.com:8080"

# 重新安裝套件
using Pkg
Pkg.add("CSV")
```

### Q3: 記憶體不足錯誤
**A**: 調整配置參數
```julia
# 使用較小的參數
config = (
    gx_size = 2,           # 減少到 2
    wins_pattern_length = 8, # 減少到 8
    n_future_draws = 1     # 減少到 1
)
```

### Q4: 數據文件格式錯誤
**A**: 檢查文件格式
```bash
# 檢查文件前幾行
head -5 data/fan5.csv

# 應該顯示類似：
# 20240101,01,15,23,31,39
# 20240102,03,12,18,25,37
```

### Q5: 程序運行緩慢
**A**: 優化性能設置
```bash
# 設置多線程
export JULIA_NUM_THREADS=4
julia -t 4 src/analyze_*.jl

# 或在 Julia 中檢查線程數
julia> Threads.nthreads()
```

### Q6: 結果文件無法生成
**A**: 檢查權限和磁碟空間
```bash
# 檢查磁碟空間
df -h

# 檢查目錄權限
ls -la results/

# 手動創建測試文件
touch results/test.txt
```

## 🔧 進階配置

### 環境變量設置
```bash
# Windows (PowerShell)
$env:JULIA_NUM_THREADS = "4"
$env:JULIA_PROJECT = "."

# macOS/Linux (Bash)
export JULIA_NUM_THREADS=4
export JULIA_PROJECT=.
```

### 啟動腳本
創建 `start.sh` (macOS/Linux) 或 `start.bat` (Windows)：

```bash
#!/bin/bash
# start.sh
export JULIA_NUM_THREADS=4
cd "$(dirname "$0")"
julia src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl
```

```batch
@echo off
REM start.bat
set JULIA_NUM_THREADS=4
cd /d "%~dp0"
julia src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl
pause
```

---

**安裝完成後，請參考《使用者手冊.md》了解詳細使用方法。**
