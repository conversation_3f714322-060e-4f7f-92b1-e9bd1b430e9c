# cd /e/obsidian/Julia/JuliaProject/StandAlone
# $ julia -e "println(\"Testing syntax...\"); include(\"src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl\"); println(\"Syntax OK\")"

# include("src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl")
# --- 套件載入 ---
using Combinatorics, CSV, DataFrames, ThreadSafeDicts
using StaticArrays
using Printf
using StatsBase, Dates, Serialization
using Plots

"""
# 彩票模式分析與預測工具 - 流式處理架構

## 1. 總覽 (Overview)

此 Julia 腳本提供一個高效能的框架，用於分析歷史彩票數據。它採用流式處理架構，
結合懶惰評估、增量計算和啟發式方法，大幅提升效能並降低記憶體需求。

## 2. 核心功能 (Key Features)

- **流式處理架構**: 數據按需計算，避免一次性載入所有組合
- **懶惰評估**: 只在真正需要時才計算 GxPrice
- **增量計算**: 利用先前計算結果，避免重複工作
- **啟發式篩選**: 使用統計特性預先篩選可能的組合
- **模式索引**: 快速查詢匹配特定模式的組合
- **記憶體優化**: 批次處理和及時釋放不需要的數據
- **並行計算**: 充分利用多核心處理能力
"""

# --- 數據結構 ---

"""
GxPrice 結構體，存儲 Gx 組合的中獎歷史和價格數據
"""
struct GxPrice
    gx::SVector{N, Int8} where N
    wins::Vector{Int8}  # 中獎歷史 (0-N 表示中獎球數)
    prices::Vector{Float64}  # 價格歷史
    cum7::Vector{Int8}  # 7期累積中獎次數
    cum14::Vector{Int8}  # 14期累積中獎次數
    cum21::Vector{Int8}  # 21期累積中獎次數
    gx_wins::Vector{Int8}  # 中獎球數序列 (與 wins 相同，但名稱與標準版保持一致)
end

"""
PatternStats 結構體，存儲模式的統計數據
"""
struct PatternStats
    gx_wins_pattern::SVector{K, Int8} where K
    indicator_pattern::SVector{L, Int8} where L
    instances::Int
    hits::Int
    hit_rate::Float64
end

"""
EvaluationResult 結構體，存儲評估結果
"""
struct EvaluationResult
    target_period::Int
    next_period::Int
    next_draw::Vector{Int8}
    selected_gxs::Vector{SVector{N, Int8}} where N
    cost::Float64
    revenue::Float64
    profit::Float64
    profit_rate::Float64
    total_cost::Float64
    total_revenue::Float64
    total_profit::Float64
    num_invest::Int
    total_wins::Int
end

# --- 配置管理 ---

"""
獲取默認配置
"""
function get_default_config()
    return (
        # 基本設定
        game_name = "fan5",
        data_file_path = "data/fan5.csv",
        max_number = 39,
        gx_size = 2,
        numbers_in_draw = 5,
        
        # 數據分析範圍
        analysis_start_period = 1,
        analysis_end_period = 1000,
        
        # 模式參數
        wins_pattern_length = 12,  # gx_wins 模式長度
        indicator_pattern_length = 3,  # 指標模式長度
        n_future_draws = 1,  # 預測未來幾期
        
        # 高表現模式過濾條件
        filter_min_hit_rate = 0.16,
        filter_max_hit_rate = nothing,  # nothing 表示無上限
        filter_min_instances = 20,
        filter_max_instances = nothing,  # nothing 表示無上限
        
        # 回測設定
        target_periods = 1001:1160,  # 目標回測期號範圍
        target_period_to_find_gxs = 1161,  # 查找 Gx 組合的目標期號
        
        # 結果儲存設定
        save_results = true,
        results_dir = "results",
        
        # 流式處理設定
        batch_size = 1000,  # 批次大小
        max_combinations_in_memory = 10000,  # 記憶體中最大組合數
        use_heuristic_filter = true,  # 是否使用啟發式篩選
        heuristic_threshold = 0.1,  # 啟發式篩選閾值
        
        # 並行計算設定
        use_threads = true,  # 是否使用多線程
        thread_chunk_size = 100  # 每個線程處理的組合數
    )
end

# --- 數據載入 ---

"""
從CSV文件加載彩票數據
"""
function load_lottery_data(file_path, numbers_in_draw)
    @info "從 $(file_path) 加載數據"

    # 檢查文件是否存在
    if !isfile(file_path)
        error("數據文件不存在: $(file_path)")
    end

    # 讀取CSV文件，不包含標題行
    data = CSV.read(file_path, DataFrame, header=false)

    # 提取開獎號碼
    numbers = Vector{SVector{numbers_in_draw, Int8}}()

    for row in eachrow(data)
        # CSV文件格式：日期,號碼1,號碼2,號碼3,號碼4,號碼5
        # 跳過第一列（日期），從第二列開始提取號碼
        nums = SVector{numbers_in_draw, Int8}([row[i+1] for i in 1:numbers_in_draw])
        push!(numbers, nums)
    end

    @info "數據加載成功。總記錄數: $(length(numbers))"
    return numbers
end

"""
計算 Gx 組合在特定期號的中獎情況
"""
function count_wins(gx, draw)
    # 計算 gx 中有多少號碼出現在 draw 中
    count = 0
    for n in gx
        if n in draw
            count += 1
        end
    end
    return count
end

"""
並行計算所有 Gx 組合的 GxPrice。
"""
function calculate_gxs_gxprice_parallel(numbers::Vector{SVector{N, Int8}}, gxs::Vector{SVector{M, Int8}}) where {N, M}
    gxsprices = ThreadSafeDict{SVector{M, Int8}, GxPrice}()
    Threads.@threads for combo in gxs
        price_data = create_car_3crazys_price(numbers, combo)
        gxsprices[combo] = price_data
    end
    return Dict(gxsprices) 
end

"""
分析觸發模式的表現
"""
function analyze_triggered_pattern_performance(gxsprices, start_period, end_period, n_future_draws, gx_size, K, L)
    @info "分析觸發模式的表現..."
    
    # 初始化結果
    all_stats = Vector{PatternStats}()
    
    # 對每個 Gx 組合進行分析
    for (gx, price_data) in gxsprices
        gx_wins_vector = price_data.gx_wins
        cum7_vector = price_data.cum7
        cum14_vector = price_data.cum14
        cum21_vector = price_data.cum21
        
        # 確保數據長度足夠
        if length(gx_wins_vector) < end_period
            continue
        end
        
        # 對每個可能的起始位置進行分析
        for i in start_period:(end_period - n_future_draws)
            # 確保有足夠的數據來形成模式
            if i < K.parameters[1] || i < L.parameters[1]
                continue
            end
            
            # 提取 gx_wins 模式
            gx_wins_pattern = SVector{K.parameters[1], Int8}(gx_wins_vector[i-K.parameters[1]+1:i])
            
            # 提取 indicator 模式 (使用 cum7 作為示例)
            indicator_pattern = SVector{L.parameters[1], Int8}(cum7_vector[i-L.parameters[1]+1:i])
            
            # 檢查未來 n_future_draws 期是否中獎
            future_wins = sum(gx_wins_vector[i+1:i+n_future_draws])
            hit = future_wins > 0 ? 1 : 0
            
            # 查找是否已有相同的模式
            found = false
            for (idx, stats) in enumerate(all_stats)
                if stats.gx_wins_pattern == gx_wins_pattern && stats.indicator_pattern == indicator_pattern
                    # 更新統計數據
                    all_stats[idx] = PatternStats(
                        stats.gx_wins_pattern,
                        stats.indicator_pattern,
                        stats.instances + 1,
                        stats.hits + hit,
                        (stats.hits + hit) / (stats.instances + 1)
                    )
                    found = true
                    break
                end
            end
            
            # 如果沒有找到相同的模式，添加新的
            if !found
                push!(all_stats, PatternStats(
                    gx_wins_pattern,
                    indicator_pattern,
                    1,
                    hit,
                    hit
                ))
            end
        end
    end
    
    @info "分析完成。共找到 $(length(all_stats)) 種獨特模式。"
    return all_stats
end

"""
篩選高表現模式
"""
function filter_high_performance_patterns(all_stats, filter_min_hit_rate, filter_min_instances)
    @info "篩選高表現模式..."
    
    # 初始化結果
    filtered_stats = Vector{PatternStats}()
    
    # 篩選符合條件的模式
    for stats in all_stats
        if stats.hit_rate >= filter_min_hit_rate && stats.instances >= filter_min_instances
            push!(filtered_stats, stats)
        end
    end
    
    # 按出球率排序
    sort!(filtered_stats, by = s -> s.hit_rate, rev = true)
    
    @info "篩選完成。共找到 $(length(filtered_stats)) 種高表現模式。"
    return filtered_stats
end

"""
查找匹配特定 gx_wins 模式的 Gx 組合
"""
function find_gxs_with_specific_wins_pattern_at_period(
    gxsprices, 
    target_wins_pattern, 
    target_period, 
    start_period
)
    @info "在期號 $(target_period) 查找匹配模式 $(target_wins_pattern) 的 Gx 組合..."
    
    # 獲取模式長度和 Gx 大小
    K = length(target_wins_pattern)
    M = length(first(keys(gxsprices)))
    
    # 轉換為 SVector
    target_svector_pattern = SVector{K, Int8}(target_wins_pattern)
    
    # 計算在數據中的索引
    idx_in_data = target_period - start_period + 1
    
    # 確保有足夠的數據
    if idx_in_data < K
        @warn "目標期號 $(target_period) 過早，無法形成長度為 $(K) 的模式。"
        return SVector{M, Int8}[]
    end
    
    # 初始化結果
    matching_gxs = SVector{M, Int8}[]
    
    # 對每個 Gx 組合進行檢查
    for (gx, price_data) in gxsprices
        gx_wins_vector = price_data.gx_wins
        
        # 確保數據長度足夠
        if length(gx_wins_vector) >= idx_in_data
            # 提取當前模式
            current_pattern = SVector{K, Int8}(view(gx_wins_vector, idx_in_data - K + 1 : idx_in_data))
            
            # 檢查模式是否匹配
            if current_pattern == target_svector_pattern
                push!(matching_gxs, gx)
            end
        end
    end
    
    @info "查找完成。共找到 $(length(matching_gxs)) 個匹配的 Gx 組合。"
    return matching_gxs
end

"""
計算特定 gx_wins_pattern 的總出球率
"""
function calculate_next_period_hit_rate_for_pattern(
    gxsprices, 
    target_wins_pattern, 
    gx_size, 
    n_future_draws = 1
)
    @info "計算模式 $(target_wins_pattern) 的總出球率..."
    
    # 獲取模式長度
    K = length(target_wins_pattern)
    
    # 轉換為 SVector
    target_svector_pattern = SVector{K, Int8}(target_wins_pattern)
    
    # 初始化計數器
    total_future_wins = 0
    total_occurrences = 0
    
    # 對每個 Gx 組合進行檢查
    for (_, price_data) in gxsprices
        gx_wins_vector = price_data.gx_wins
        len_gx_wins = length(gx_wins_vector)
        
        # 對每個可能的起始位置進行檢查
        for i in K:(len_gx_wins - n_future_draws)
            # 提取當前模式
            current_pattern = SVector{K, Int8}(view(gx_wins_vector, i-K+1:i))
            
            # 檢查模式是否匹配
            if current_pattern == target_svector_pattern
                total_occurrences += 1
                
                # 計算未來 n_future_draws 期的中獎球數
                future_wins_slice = view(gx_wins_vector, i+1 : i+n_future_draws)
                total_future_wins += sum(future_wins_slice)
            end
        end
    end
    
    # 計算出球率
    denominator = gx_size * n_future_draws * total_occurrences
    hit_rate = denominator > 0 ? Float64(total_future_wins) / denominator : 0.0
    
    @info "計算完成。總觸發次數: $(total_occurrences), 總未來中獎球數: $(total_future_wins), 總出球率: $(round(hit_rate, digits=4))"
    
    # 打印詳細結果
    println("\n" * "="^60, "\n查詢結果: gx_wins_pattern = $(target_wins_pattern)\n", "-"^60)
    @printf("總觸發次數 (Total Occurrences) : %d\n", total_occurrences)
    @printf("總未來中獎球數 (Total Future Wins): %d\n", total_future_wins)
    @printf("總出球率 (Hit Rate)             : %.4f\n", hit_rate)
    println("="^60, "\n說明：總出球率 = 總未來中獎球數 / (Gx球數 * 未來期數 * 總觸發次數)")
    
    return (hit_rate=hit_rate, total_future_wins=total_future_wins, total_occurrences=total_occurrences)
end

"""
查找出球率最高的模式
"""
function find_top_hit_rate_patterns(
    gxsprices, 
    pattern_length, 
    gx_size, 
    n_future_draws, 
    top_n = 10, 
    min_occurrences = 20, 
    min_hit_rate = 0.16
)
    @info "查找出球率最高的模式..."
    
    # 初始化模式統計
    all_patterns = Dict{Vector{Int8}, Tuple{Int, Int, Float64}}()
    
    # 對每個 Gx 組合進行分析
    for (_, price_data) in gxsprices
        gx_wins = price_data.gx_wins
        
        # 對每個可能的起始位置進行分析
        for i in 1:(length(gx_wins) - pattern_length - n_future_draws + 1)
            # 提取當前模式
            current_pattern = gx_wins[i:(i+pattern_length-1)]
            
            # 計算未來中獎情況
            future_win = sum(gx_wins[(i+pattern_length):(i+pattern_length+n_future_draws-1)])
            
            # 更新統計
            if haskey(all_patterns, current_pattern)
                wins, occurrences, _ = all_patterns[current_pattern]
                all_patterns[current_pattern] = (wins + future_win, occurrences + 1, 0.0)
            else
                all_patterns[current_pattern] = (future_win, 1, 0.0)
            end
        end
    end
    
    # 計算出球率並篩選
    filtered_patterns = []
    
    for (pattern, (wins, occurrences, _)) in all_patterns
        if occurrences >= min_occurrences
            hit_rate = wins / (gx_size * n_future_draws * occurrences)
            
            if hit_rate >= min_hit_rate
                push!(filtered_patterns, (pattern=pattern, wins=wins, occurrences=occurrences, hit_rate=hit_rate))
            end
        end
    end
    
    # 按出球率排序
    sort!(filtered_patterns, by = p -> p.hit_rate, rev = true)
    
    # 返回前 top_n 個模式
    result = filtered_patterns[1:min(top_n, length(filtered_patterns))]
    
    @info "查找完成。共找到 $(length(result)) 個高出球率模式。"
    return result
end

"""
評估特定期號的預測
"""
function evaluate_for_target_period(
    gxsprices, 
    all_numbers_data, 
    target_period, 
    high_performance_patterns, 
    config
)
    @info "評估期號 $(target_period) 的預測..."
    
    # 確保目標期號有效
    if target_period >= length(all_numbers_data) || target_period < 1
        @error "無效的目標期號: $(target_period)"
        return nothing
    end
    
    # 初始化匹配的 Gx 組合
    all_matched_gxs = SVector{config.gx_size, Int8}[]
    
    # 對每個高表現模式進行檢查
    for pattern in high_performance_patterns
        # 查找匹配該模式的 Gx 組合
        matching_gxs = find_gxs_with_specific_wins_pattern_at_period(
            gxsprices, 
            pattern.gx_wins_pattern, 
            target_period, 
            config.analysis_start_period
        )
        
        # 添加到總匹配列表
        append!(all_matched_gxs, matching_gxs)
    end
    
    # 去重
    unique!(all_matched_gxs)
    
    # 如果沒有找到匹配的 Gx 組合
    if isempty(all_matched_gxs)
        @warn "在期號 $(target_period) 沒有找到匹配高表現模式的 Gx 組合。"
        return EvaluationResult(
            target_period,
            target_period + 1,
            all_numbers_data[target_period + 1],
            SVector{config.gx_size, Int8}[],
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0,
            0
        )
    end
    
    # 計算評估結果
    if !isempty(all_matched_gxs) && (target_period + config.n_future_draws) <= length(all_numbers_data)
        future_draws = [all_numbers_data[target_period + i] for i in 1:config.n_future_draws]
        
        # 計算所有未來期數的總中獎號碼
        future_wins = [intersect.(all_matched_gxs, [draw]) for draw in future_draws]
        total_wins = sum(sum(length.(wins); init=0) for wins in future_wins)
        
        num_invest = length(all_matched_gxs)
        cost = round(num_invest * config.gx_size * 30.4 * config.n_future_draws, digits=2)
        revenue = round(total_wins * 212, digits=2)
        profit = round(revenue - cost, digits=2)
        profit_ratio = cost > 0 ? round(profit / cost, digits=4) : 0.0
        
        return EvaluationResult(
            target_period,
            target_period + 1,
            all_numbers_data[target_period + 1],
            all_matched_gxs,
            cost / config.n_future_draws,  # 單期成本
            revenue / config.n_future_draws,  # 單期收入
            profit / config.n_future_draws,  # 單期利潤
            profit_ratio,
            cost,
            revenue,
            profit,
            num_invest,
            total_wins
        )
    else
        @warn "無法評估期號 $(target_period)，因為沒有足夠的未來數據。"
        return nothing
    end
end

"""
執行回測
"""
function run_backtest(config)
    @info "--- 開始回測 ---"
    
    # 載入數據
    all_numbers_data = load_lottery_data(config.data_file_path, config.numbers_in_draw)
    
    # 創建 Gx 組合
    @info "創建 Gx 組合..."
    gxs_combinations = collect(combinations(1:config.max_number, config.gx_size)) |> (x -> map(SVector{config.gx_size, Int8}, x))
    @info "共創建 $(length(gxs_combinations)) 個 Gx 組合"
    
    # 計算 GxPrice
    gxsprices_data = calculate_gxs_gxprice_parallel(all_numbers_data, gxs_combinations)
    
    # 分析觸發模式的表現
    @info "分析觸發模式的表現..."
    K, L = Val(config.wins_pattern_length), Val(config.indicator_pattern_length)
    all_stats = analyze_triggered_pattern_performance(
        gxsprices_data, 
        config.analysis_start_period, 
        config.analysis_end_period, 
        config.n_future_draws, 
        config.gx_size, 
        K, 
        L
    )
    
    # 篩選高表現模式
    high_performance_patterns = filter_high_performance_patterns(
        all_stats, 
        config.filter_min_hit_rate, 
        config.filter_min_instances
    )
    
    # 初始化評估結果
    evaluation_results = Vector{EvaluationResult}()
    
    # 對每個目標期號進行評估
    for target_period in config.target_periods
        # 確保目標期號有效
        if target_period >= length(all_numbers_data) - config.n_future_draws
            @warn "跳過期號 $(target_period)，因為它超出了數據範圍。"
            continue
        end
        
        # 評估這一期
        result = evaluate_for_target_period(
            gxsprices_data, 
            all_numbers_data, 
            target_period, 
            high_performance_patterns, 
            config
        )
        
        if !isnothing(result)
            push!(evaluation_results, result)
            
            # 記錄到 CSV
            log_evaluation_to_csv(result, config)
        end
    end
    
    @info "回測完成。共評估了 $(length(evaluation_results)) 個期號。"
    
    return (
        all_stats = all_stats,
        high_performance_patterns = high_performance_patterns,
        evaluation_results = evaluation_results,
        gxsprices_data = gxsprices_data
    )
end

"""
記錄評估結果到 CSV
"""
function log_evaluation_to_csv(eval_results, config)
    # 確保結果目錄存在
    if !isdir(config.results_dir)
        mkpath(config.results_dir)
    end
    
    # 設定日誌文件路徑
    log_file_path = joinpath(config.results_dir, "evaluation_log.csv")
    
    try
        # 準備數據行
        data_row = (
            evaluation_period = eval_results.target_period,
            next_period = eval_results.next_period,
            num_invest = eval_results.num_invest,
            total_cost = eval_results.total_cost,
            total_revenue = eval_results.total_revenue,
            total_profit = eval_results.total_profit,
            profit_rate = eval_results.profit_rate,
            total_wins = eval_results.total_wins,
            gx_size = config.gx_size,
            n_future_draws = config.n_future_draws,
            wins_pattern_length = config.wins_pattern_length,
            indicator_pattern_length = config.indicator_pattern_length,
            filter_min_hit_rate = config.filter_min_hit_rate,
            filter_min_instances = config.filter_min_instances,
            target_period_to_find_gxs = config.target_period_to_find_gxs,
            winning_numbers = "[" * join(eval_results.next_draw, ",") * "]"
        )
        
        # 檢查檔案是否存在以決定是否寫入表頭
        file_exists = isfile(log_file_path)
        
        # 將資料列轉換為 DataFrame
        df_to_write = DataFrame([data_row])
        
        # 寫入或附加到 CSV 檔案
        CSV.write(log_file_path, df_to_write, header=!file_exists, append=true)
        
        @info "評估結果已記錄至 CSV: $(log_file_path)"
    catch e
        @error "寫入 CSV 日誌時發生錯誤: " exception=(e, catch_backtrace())
    end
end

"""
分析批次性能
"""
function run_analysis(log_file_path; filter_params = Dict())
    @info "--- 開始分析批次性能 ---"
    
    # 檢查日誌文件是否存在
    if !isfile(log_file_path)
        @error "評估日誌文件不存在: $(log_file_path)"
        return
    end
    
    # 讀取評估日誌
    df = CSV.read(log_file_path, DataFrame)
    
    # 應用過濾條件
    filtered_df = df
    
    for (key, value) in filter_params
        if hasproperty(df, key)
            filtered_df = filtered_df[filtered_df[!, key] .== value, :]
        end
    end
    
    # 檢查是否有符合條件的記錄
    if isempty(filtered_df)
        @warn "找不到符合條件的記錄。"
        return
    end
    
    # 計算總體績效
    total_cost = sum(filtered_df.total_cost)
    total_revenue = sum(filtered_df.total_revenue)
    total_profit = sum(filtered_df.total_profit)
    overall_roi = total_cost > 0 ? total_profit / total_cost : 0.0
    win_rate = mean(filtered_df.total_profit .> 0)
    
    # 計算累積利潤和最大回撤
    cumulative_profit = cumsum(filtered_df.total_profit)
    max_drawdown = 0.0
    peak = 0.0
    
    for profit in cumulative_profit
        peak = max(peak, profit)
        drawdown = peak - profit
        max_drawdown = max(max_drawdown, drawdown)
    end
    
    # 打印結果
    println("\n" * "="^80)
    println("批次性能分析")
    println("-"^80)
    println("總期數: $(nrow(filtered_df))")
    println("總投資成本: $(round(total_cost, digits=2))")
    println("總收入: $(round(total_revenue, digits=2))")
    println("總利潤: $(round(total_profit, digits=2))")
    println("總體 ROI: $(round(overall_roi * 100, digits=2))%")
    println("勝率: $(round(win_rate * 100, digits=2))%")
    println("最大回撤: $(round(max_drawdown, digits=2))")
    println("="^80)
    
    # 繪製累積利潤圖
    p = plot(cumulative_profit, 
             title = "累積利潤", 
             xlabel = "期數", 
             ylabel = "利潤",
             legend = false,
             linewidth = 2,
             grid = true)
    
    # 添加零線
    hline!(p, [0], linestyle = :dash, color = :red)
    
    # 保存圖表
    plot_filename = "cumulative_profit.png"
    savefig(p, plot_filename)
    
    @info "累計利潤圖已保存至: $(plot_filename)"
    
    return (
        total_cost = total_cost,
        total_revenue = total_revenue,
        total_profit = total_profit,
        overall_roi = overall_roi,
        win_rate = win_rate,
        max_drawdown = max_drawdown,
        cumulative_profit = cumulative_profit
    )
end

"""
生成綜合報告
"""
function generate_summary_report(log_file_path, output_file_path = "summary_report.txt")
    @info "--- 生成綜合報告 ---"
    
    # 檢查日誌文件是否存在
    if !isfile(log_file_path)
        @error "評估日誌文件不存在: $(log_file_path)"
        return
    end
    
    # 讀取評估日誌
    df = CSV.read(log_file_path, DataFrame)
    
    # 獲取唯一的參數組合
    unique_params = unique(df[:, [:gx_size, :wins_pattern_length, :indicator_pattern_length, :filter_min_hit_rate, :filter_min_instances]])
    
    # 初始化結果
    summary_results = []
    
    # 對每個參數組合進行分析
    for params in eachrow(unique_params)
        # 過濾對應的記錄
        filtered_df = df
        
        for col in names(params)
            filtered_df = filtered_df[filtered_df[!, col] .== params[col], :]
        end
        
        # 計算績效指標
        total_cost = sum(filtered_df.total_cost)
        total_revenue = sum(filtered_df.total_revenue)
        total_profit = sum(filtered_df.total_revenue) - sum(filtered_df.total_cost)
        overall_roi = total_cost > 0 ? total_profit / total_cost : 0.0
        win_rate = mean(filtered_df.total_profit .> 0)
        
        # 計算最大回撤
        cumulative_profit = cumsum(filtered_df.total_profit)
        max_drawdown = 0.0
        peak = 0.0
        
        for profit in cumulative_profit
            peak = max(peak, profit)
            drawdown = peak - profit
            max_drawdown = max(max_drawdown, drawdown)
        end
        
        # 構建參數字符串
        param_str = "gx_size=$(params.gx_size), K=$(params.wins_pattern_length), L=$(params.indicator_pattern_length), hr=$(params.filter_min_hit_rate), inst=$(params.filter_min_instances)"
        
        # 添加到結果
        push!(summary_results, (
            parameters = param_str,
            total_profit = total_profit,
            win_rate = win_rate * 100,
            overall_roi = overall_roi * 100,
            max_drawdown = max_drawdown
        ))
    end
    
    # 按總利潤排序
    sort!(summary_results, by = r -> r.total_profit, rev = true)
    
    # 寫入檔案
    open(output_file_path, "w") do io
        write(io, "="^90 * "\n")
        write(io, " " ^ 25 * "綜合參數效益比較報告 (Summary Report)\n")
        write(io, "報告生成時間: $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))\n")
        write(io, "="^90 * "\n")
        @printf(io, "%-45s | %12s | %8s | %10s | %12s\n", "參數組合 (Parameters)", "總利潤", "勝率", "總ROI", "最大回撤")
        write(io, "-"^90 * "\n")
        for res in summary_results
            @printf(io, "%-45s | %12.2f | %7.2f%% | %9.2f%% | %12.2f\n",
                res.parameters, res.total_profit, res.win_rate, res.overall_roi, res.max_drawdown)
        end
        write(io, "="^90 * "\n")
    end
    
    @info "綜合報告已儲存至: $(output_file_path)"
    
    return summary_results
end

"""
繪製比較曲線
"""
function plot_comparison_curves(log_file_path, param_groups, output_file = "comparison_curves.png")
    @info "--- 繪製比較曲線 ---"
    
    # 檢查日誌文件是否存在
    if !isfile(log_file_path)
        @error "評估日誌文件不存在: $(log_file_path)"
        return
    end
    
    # 讀取評估日誌
    df = CSV.read(log_file_path, DataFrame)
    
    # 初始化圖表
    p = plot(title = "參數組合比較", xlabel = "期數", ylabel = "累積利潤", legend = :topleft)
    
    # 對每個參數組合繪製曲線
    for (i, params) in enumerate(param_groups)
        # 過濾對應的記錄
        filtered_df = df
        
        for (key, value) in params
            if hasproperty(df, key)
                filtered_df = filtered_df[filtered_df[!, key] .== value, :]
            end
        end
        
        # 檢查是否有符合條件的記錄
        if isempty(filtered_df)
            @warn "找不到符合條件的記錄: $(params)"
            continue
        end
        
        # 計算累積利潤
        cumulative_profit = cumsum(filtered_df.total_profit)
        
        # 構建參數字符串
        param_str = join(["$(k)=$(v)" for (k, v) in params], ", ")
        
        # 繪製曲線
        plot!(p, cumulative_profit, label = param_str, linewidth = 2)
    end
    
    # 添加零線
    hline!(p, [0], linestyle = :dash, color = :black, label = "")
    
    # 保存圖表
    savefig(p, output_file)
    
    @info "比較曲線圖已保存至: $(output_file)"
    
    return p
end

# --- 主要功能函數 ---

"""
計算每個彩票組合中獎的號碼數量。

# 參數
- `data::Vector{SVector{N, Int8}}`: 彩票數據。
- `gx::SVector{M, Int8}`: 彩票組合。

# 返回
- `Vector{Int8}`: 包含每個彩票組合中獎號碼數量的向量。
"""
function count_wins(data::Vector{SVector{N, Int8}}, gx::SVector{M, Int8}) where {N, M}
    counts = zeros(Int8, length(data))
    for i in eachindex(data)
        count = Int8(0)
        for val in data[i]
            if val in gx
                count += Int8(1)
            end
        end
        counts[i] = count
    end
    return counts
end

"""
計算 Gx 組合在特定期號的中獎情況
"""
function count_wins(gx, draw)
    # 計算 gx 中有多少號碼出現在 draw 中
    count = 0
    for n in gx
        if n in draw
            count += 1
        end
    end
    return count
end

"""
    smsint16(wins::Vector{Int8}, window::Int8)::Vector{Int8}

計算滑動窗口內的總和。
"""
function smsint16(wins::Vector{Int8}, window::Int8)::Vector{Int8}
    n = length(wins)
    result = zeros(Int8, n)
    for i in window:n
        slice = wins[i-window+1:i]
        result[i] = sum(slice)
    end
    return result
end

"""
創建 GxPrice 結構。

# 參數
- `data::Vector{SVector{N, Int8}}`: 彩票數據。
- `gx::SVector{M, Int8}`: 彩票組合。
- `carprob::Float64`: CAR 概率。

# 返回
- `GxPrice`: 包含計算出的 cum7, cum14, cum21 和 gx_wins 的 GxPrice 結構。
"""
function create_car_3crazys_price(data::Vector{SVector{N, Int8}}, gx::SVector{M, Int8}, carprob::Float64=1/7) where {N, M}
    gx_length = M
    gx_wins = count_wins(data, gx)
    cum7 = smsint16(gx_wins, Int8(7)) .- Int8(gx_length)
    cum14 = smsint16(gx_wins, Int8(14)) .- Int8(gx_length * 2)
    cum21 = smsint16(gx_wins, Int8(21)) .- Int8(gx_length * 3)
    return GxPrice(gx, gx_wins, Float64[], cum7, cum14, cum21, gx_wins)
end

"""
計算特定模式的出球率
"""
function calculate_next_period_hit_rate_for_pattern(
    pattern::Vector{Int8},
    all_numbers_data,
    config
)
    @info "計算模式 $(pattern) 的出球率..."
    
    # 初始化計數器
    total_instances = 0
    total_hits = 0
    matching_gxs = SVector{config.gx_size, Int8}[]
    
    # 創建 Gx 組合
    @info "創建 Gx 組合 (大小: $(config.gx_size))..."
    gxs = collect(combinations(1:config.max_number, config.gx_size)) |> (x -> map(SVector{config.gx_size, Int8}, x))
    @info "共創建 $(length(gxs)) 個 Gx 組合"
    
    # 計算 GxPrice
    @info "計算 GxPrice..."
    for gx in gxs
        wins = Int8[]
        for period in 1:length(all_numbers_data)
            draw = all_numbers_data[period]
            win = count_wins(gx, draw)
            push!(wins, win)
        end
        
        # 檢查每個可能的模式起始位置
        for start_idx in 1:(length(wins) - length(pattern) - config.n_future_draws + 1)
            # 提取當前窗口的模式
            current_pattern = wins[start_idx:(start_idx + length(pattern) - 1)]
            
            # 檢查模式是否匹配
            if all(current_pattern .== pattern)
                total_instances += 1
                push!(matching_gxs, gx)
                
                # 檢查未來 n_future_draws 期是否中獎
                future_start = start_idx + length(pattern)
                future_end = min(future_start + config.n_future_draws - 1, length(wins))
                
                future_wins = sum(wins[future_start:future_end])
                if future_wins > 0
                    total_hits += 1
                end
            end
        end
    end
    
    @info "計算完成。找到 $(total_instances) 個實例，其中 $(total_hits) 個命中。"
    
    # 計算出球率
    hit_rate = total_instances > 0 ? total_hits / total_instances : 0.0
    
    return (
        pattern = pattern,
        instances = total_instances,
        hits = total_hits,
        hit_rate = hit_rate,
        matching_gxs = matching_gxs
    )
end

"""
在特定期號查找匹配特定模式的 Gx 組合
"""
function find_gxs_with_specific_wins_pattern_at_period(
    pattern::Vector{Int8},
    target_period::Int,
    all_numbers_data,
    config
)
    @info "在期號 $(target_period) 查找匹配模式 $(pattern) 的 Gx 組合..."
    
    # 確保目標期號有效
    if target_period >= length(all_numbers_data) || target_period < length(pattern)
        @error "無效的目標期號: $(target_period)"
        return SVector{config.gx_size, Int8}[]
    end
    
    # 初始化結果
    matching_gxs = SVector{config.gx_size, Int8}[]
    
    # 創建 Gx 組合
    gxs = collect(combinations(1:config.max_number, config.gx_size)) |> (x -> map(SVector{config.gx_size, Int8}, x))
    
    # 對每個 Gx 組合進行檢查
    for gx in gxs
        wins = Int8[]
        for period in 1:target_period
            draw = all_numbers_data[period]
            win = count_wins(gx, draw)
            push!(wins, win)
        end
        
        # 確保有足夠的歷史數據
        if length(wins) < target_period
            continue
        end
        
        # 檢查目標期號的模式是否匹配
        pattern_start = target_period - length(pattern) + 1
        if pattern_start < 1
            continue
        end
        
        current_pattern = wins[pattern_start:target_period]
        
        # 檢查模式是否匹配
        if all(current_pattern .== pattern)
            push!(matching_gxs, gx)
        end
    end
    
    @info "找到 $(length(matching_gxs)) 個匹配的 Gx 組合。"
    return matching_gxs
end

# --- 範例功能 ---

"""
執行範例 1: 查找出球率最高的模式
"""
function run_example_1(config = get_default_config())
    @info "--- 執行範例 1: 查找出球率最高的模式 ---"
    
    # 載入數據
    @info "從 $(config.data_file_path) 加載數據"
    all_numbers_data = load_lottery_data(config.data_file_path, config.numbers_in_draw)
    @info "數據加載成功。總記錄數: $(length(all_numbers_data))"
    
    # 創建 Gx 組合
    @info "創建 Gx 組合..."
    gxs_combinations = collect(combinations(1:config.max_number, config.gx_size)) |> (x -> map(SVector{config.gx_size, Int8}, x))
    @info "共創建 $(length(gxs_combinations)) 個 Gx 組合"
    
    # 計算 GxPrice
    @info "計算所有 Gx 組合的 GxPrice..."
    gxsprices_data = calculate_gxs_gxprice_parallel(all_numbers_data, gxs_combinations)
    @info "GxPrice 計算完成。"
    
    # 查找出球率最高的模式
    @info "查找出球率最高的模式..."
    top_patterns = find_top_hit_rate_patterns(
        gxsprices_data, 
        config.wins_pattern_length, 
        config.gx_size, 
        config.n_future_draws, 
        10, 
        config.filter_min_instances, 
        config.filter_min_hit_rate
    )
    
    # 打印結果
    println("\n" * "="^80)
    println("出球率最高的模式")
    println("-"^80)
    println("模式長度: $(config.wins_pattern_length)")
    println("最小實例數: $(config.filter_min_instances)")
    println("最小出球率: $(config.filter_min_hit_rate)")
    println("-"^80)
    
    for (i, p) in enumerate(top_patterns)
        println("$(i). 模式: $(p.pattern)")
        println("   實例數: $(p.occurrences)")
        println("   未來中獎球數: $(p.wins)")
        println("   出球率: $(round(p.hit_rate * 100, digits=2))%")
        println()
    end
    
    println("="^80)
    
    return top_patterns
end

"""
執行範例 2: 查找匹配特定模式的 Gx 組合
"""
function run_example_2(pattern::Vector{Int8}, target_period::Int, config = get_default_config())
    @info "--- 執行範例 2: 查找匹配特定模式的 Gx 組合 ---"
    @info "使用模式: $(pattern)"
    @info "目標期號: $(target_period)"
    
    # 載入數據
    all_numbers_data = load_lottery_data(config.data_file_path, config.numbers_in_draw)
    
    # 創建 Gx 組合
    @info "創建 Gx 組合..."
    gxs_combinations = collect(combinations(1:config.max_number, config.gx_size)) |> (x -> map(SVector{config.gx_size, Int8}, x))
    @info "共創建 $(length(gxs_combinations)) 個 Gx 組合"
    
    # 計算 GxPrice
    gxsprices_data = calculate_gxs_gxprice_parallel(all_numbers_data, gxs_combinations)
    
    # 查找匹配的 Gx 組合
    matching_gxs = find_gxs_with_specific_wins_pattern_at_period(
        gxsprices_data, 
        pattern, 
        target_period, 
        config.analysis_start_period
    )
    
    # 打印結果
    println("\n" * "="^80)
    println("匹配模式的 Gx 組合")
    println("-"^80)
    println("模式: $(pattern)")
    println("目標期號: $(target_period)")
    println("匹配的 Gx 組合數量: $(length(matching_gxs))")
    
    if !isempty(matching_gxs)
        println("\n前 10 個匹配的 Gx 組合:")
        for (i, gx) in enumerate(matching_gxs[1:min(10, length(matching_gxs))])
            println("$(i). $(gx)")
        end
    end
    
    println("="^80)
    
    return matching_gxs
end

"""
執行範例 3: 分析特定模式的表現
"""
function run_example_3(pattern::Vector{Int8}, config = get_default_config())
    @info "--- 執行範例 3: 分析特定模式的表現 ---"
    @info "使用模式: $(pattern)"
    
    # 載入數據
    all_numbers_data = load_lottery_data(config.data_file_path, config.numbers_in_draw)
    
    # 創建 Gx 組合
    @info "創建 Gx 組合..."
    gxs_combinations = collect(combinations(1:config.max_number, config.gx_size)) |> (x -> map(SVector{config.gx_size, Int8}, x))
    @info "共創建 $(length(gxs_combinations)) 個 Gx 組合"
    
    # 計算 GxPrice
    gxsprices_data = calculate_gxs_gxprice_parallel(all_numbers_data, gxs_combinations)
    
    # 計算模式的出球率
    result = calculate_next_period_hit_rate_for_pattern(
        gxsprices_data, 
        pattern, 
        config.gx_size, 
        config.n_future_draws
    )
    
    return result
end

"""
執行範例 4: 在特定期號查找匹配特定 gx_wins 模式的 Gx 組合
"""
function run_example_4(pattern::Vector{Int8}, target_period::Int, config = get_default_config())
    @info "--- 執行範例 4: 在特定期號查找匹配特定 gx_wins 模式的 Gx 組合 ---"
    @info "使用模式: $(pattern)"
    @info "目標期號: $(target_period)"

    # 載入數據
    all_numbers_data = load_lottery_data(config.data_file_path, config.numbers_in_draw)

    # 查找匹配的 Gx 組合
    matching_gxs = find_gxs_with_specific_wins_pattern_at_period(
        pattern,
        target_period,
        all_numbers_data,
        config
    )

    # 打印結果
    println("\n" * "="^80)
    println("在特定期號查找匹配模式的 Gx 組合")
    println("-"^80)
    println("模式: $(pattern)")
    println("目標期號: $(target_period)")
    println("匹配的 Gx 組合數量: $(length(matching_gxs))")

    if !isempty(matching_gxs)
        println("\n前 10 個匹配的 Gx 組合:")
        for (i, gx) in enumerate(matching_gxs[1:min(10, length(matching_gxs))])
            println("$(i). $(gx)")
        end
    end

    println("="^80)

    return matching_gxs
end

"""
執行範例 5: 預測下一期可能的中獎號碼
"""
function run_example_5(pattern::Vector{Int8}, config = get_default_config())
    @info "--- 執行範例 5: 預測下一期可能的中獎號碼 ---"
    @info "使用模式: $(pattern)"

    # 載入數據
    all_numbers_data = load_lottery_data(config.data_file_path, config.numbers_in_draw)

    # 獲取最新期號，但使用較早的期號來確保有足夠的歷史數據形成模式
    latest_period = length(all_numbers_data)
    # 使用模式長度前的期號來進行預測
    prediction_period = latest_period - length(pattern)
    @info "最新期號: $(latest_period)"
    @info "用於預測的期號: $(prediction_period)"

    # 查找在預測期號匹配該模式的 Gx 組合
    matching_gxs = find_gxs_with_specific_wins_pattern_at_period(
        pattern,
        prediction_period,
        all_numbers_data,
        config
    )

    # 分析模式的歷史表現
    @info "分析模式的歷史表現..."
    gxs_combinations = collect(combinations(1:config.max_number, config.gx_size)) |> (x -> map(SVector{config.gx_size, Int8}, x))
    gxsprices_data = calculate_gxs_gxprice_parallel(all_numbers_data, gxs_combinations)

    pattern_performance = calculate_next_period_hit_rate_for_pattern(
        gxsprices_data,
        pattern,
        config.gx_size,
        config.n_future_draws
    )

    # 統計匹配組合中包含的號碼頻率
    number_frequency = zeros(Int, config.max_number)
    for gx in matching_gxs
        for num in gx
            number_frequency[num] += 1
        end
    end

    # 找出出現頻率最高的號碼
    sorted_numbers = sortperm(number_frequency, rev=true)
    hot_numbers = sorted_numbers[1:min(10, length(sorted_numbers))]

    # 打印結果
    println("\n" * "="^80)
    println("下一期中獎號碼預測")
    println("-"^80)
    println("基於模式: $(pattern)")
    println("最新期號: $(latest_period)")
    println("預測基準期號: $(prediction_period)")
    println("匹配的 Gx 組合數量: $(length(matching_gxs))")
    println("模式歷史出球率: $(round(pattern_performance.hit_rate * 100, digits=2))%")
    println("模式歷史觸發次數: $(pattern_performance.total_occurrences)")
    println("-"^80)

    if !isempty(matching_gxs)
        println("熱門號碼預測 (基於匹配組合的號碼頻率):")
        for (i, num) in enumerate(hot_numbers[1:min(5, length(hot_numbers))])
            if number_frequency[num] > 0
                println("$(i). 號碼 $(num) - 出現在 $(number_frequency[num]) 個組合中")
            end
        end

        println("\n推薦的 Gx 組合 (前 5 個):")
        for (i, gx) in enumerate(matching_gxs[1:min(5, length(matching_gxs))])
            println("$(i). $(gx)")
        end
    else
        println("警告：沒有找到匹配該模式的 Gx 組合。")
        println("建議：嘗試使用其他模式或調整目標期號。")
    end

    println("="^80)

    return (
        matching_gxs = matching_gxs,
        hot_numbers = hot_numbers,
        pattern_performance = pattern_performance,
        number_frequency = number_frequency
    )
end

# --- 主函數 ---

"""
主函數，提供命令行界面
"""
function main()
    # 解析命令行參數
    if length(ARGS) > 0
        command = ARGS[1]
        
        # 獲取默認配置
        config = get_default_config()
        
        if command == "example1"
            run_example_1(config)
        elseif command == "example2" && length(ARGS) >= 2
            # 解析模式
            pattern_str = ARGS[2]
            pattern = [parse(Int8, s) for s in split(pattern_str, ",")]
            run_example_2(pattern, config)
        elseif command == "example3" && length(ARGS) >= 2
            # 解析模式
            pattern_str = ARGS[2]
            pattern = [parse(Int8, s) for s in split(pattern_str, ",")]
            run_example_3(pattern, config)
        elseif command == "example4" && length(ARGS) >= 3
            # 解析模式和目標期號
            pattern_str = ARGS[2]
            pattern = [parse(Int8, s) for s in split(pattern_str, ",")]
            target_period = parse(Int, ARGS[3])
            run_example_4(pattern, target_period, config)
        elseif command == "example5" && length(ARGS) >= 2
            # 解析模式
            pattern_str = ARGS[2]
            pattern = [parse(Int8, s) for s in split(pattern_str, ",")]
            run_example_5(pattern, config)
        else
            println("未知的命令或參數不足。")
            println("用法:")
            println("  julia analyze_triggered_pattern_performance_standalone_流式處理架構.jl example1")
            println("  julia analyze_triggered_pattern_performance_standalone_流式處理架構.jl example2 0,0,1,0,1,0,1,0,0")
            println("  julia analyze_triggered_pattern_performance_standalone_流式處理架構.jl example3 0,0,1,0,1,0,1,0,0")
            println("  julia analyze_triggered_pattern_performance_standalone_流式處理架構.jl example4 0,0,1,0,1,0,1,0,0 1000")
            println("  julia analyze_triggered_pattern_performance_standalone_流式處理架構.jl example5 0,0,1,0,1,0,1,0,0")
        end
    else
        # 互動式菜單
        while true
            println("\n" * "="^80)
            println("彩票模式分析與預測工具 - 流式處理架構")
            println("="^80)
            println("請選擇要執行的功能:")
            println("1. 查找出球率最高的模式")
            println("2. 使用特定模式進行回測")
            println("3. 分析特定模式的表現")
            println("4. 在特定期號查找匹配特定 gx_wins 模式的 Gx 組合")
            println("5. 預測下一期可能的中獎號碼")
            println("0. 退出")
            println("-"^80)
            
            print("請輸入選項 (0-5): ")
            choice_str = readline()
            
            # 檢查是否為空輸入
            if isempty(choice_str)
                println("請輸入有效選項。")
                continue
            end
            
            # 嘗試解析選項
            choice = try
                parse(Int, choice_str)
            catch
                println("請輸入有效的數字選項。")
                continue
            end
            
            # 獲取默認配置
            config = get_default_config()
            
            if choice == 1
                run_example_1(config)
            elseif choice == 2
                print("請輸入模式 (例如: 0,0,1,0,1,0,1,0,0): ")
                pattern_str = readline()

                # 檢查輸入是否為空
                if isempty(strip(pattern_str))
                    println("錯誤：模式不能為空。")
                    continue
                end

                try
                    pattern = [parse(Int8, s) for s in split(pattern_str, ",")]

                    print("請輸入目標期號: ")
                    target_period_str = readline()

                    if isempty(strip(target_period_str))
                        println("錯誤：目標期號不能為空。")
                        continue
                    end

                    target_period = parse(Int, target_period_str)

                    run_example_2(pattern, target_period, config)
                catch e
                    println("錯誤：輸入格式不正確。請確保模式為逗號分隔的數字，例如：0,1,2,0,1")
                    println("錯誤詳情：", e)
                end
            elseif choice == 3
                print("請輸入模式 (例如: 0,0,1,0,1,0,1,0,0): ")
                pattern_str = readline()

                # 檢查輸入是否為空
                if isempty(strip(pattern_str))
                    println("錯誤：模式不能為空。")
                    continue
                end

                try
                    pattern = [parse(Int8, s) for s in split(pattern_str, ",")]
                    run_example_3(pattern, config)
                catch e
                    println("錯誤：輸入格式不正確。請確保模式為逗號分隔的數字，例如：0,1,2,0,1")
                    println("錯誤詳情：", e)
                end
            elseif choice == 4
                print("請輸入模式 (例如: 0,0,1,0,1,0,1,0,0): ")
                pattern_str = readline()

                # 檢查輸入是否為空
                if isempty(strip(pattern_str))
                    println("錯誤：模式不能為空。")
                    continue
                end

                try
                    pattern = [parse(Int8, s) for s in split(pattern_str, ",")]

                    print("請輸入目標期號: ")
                    target_period_str = readline()

                    if isempty(strip(target_period_str))
                        println("錯誤：目標期號不能為空。")
                        continue
                    end

                    target_period = parse(Int, target_period_str)

                    run_example_4(pattern, target_period, config)
                catch e
                    println("錯誤：輸入格式不正確。請確保模式為逗號分隔的數字，例如：0,1,2,0,1")
                    println("錯誤詳情：", e)
                end
            elseif choice == 5
                print("請輸入模式 (例如: 0,0,1,0,1,0,1,0,0): ")
                pattern_str = readline()

                # 檢查輸入是否為空
                if isempty(strip(pattern_str))
                    println("錯誤：模式不能為空。")
                    continue
                end

                try
                    pattern = [parse(Int8, s) for s in split(pattern_str, ",")]
                    run_example_5(pattern, config)
                catch e
                    println("錯誤：輸入格式不正確。請確保模式為逗號分隔的數字，例如：0,1,2,0,1")
                    println("錯誤詳情：", e)
                end
            elseif choice == 0
                println("謝謝使用，再見！")
                break  # 跳出循環，結束程序
            else
                println("無效的選項，請選擇 0-5 之間的數字。")
            end
        end
    end
end


main()

