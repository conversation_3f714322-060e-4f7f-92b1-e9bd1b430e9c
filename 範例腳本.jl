# 彩票模式分析工具 - 範例腳本集合
# 這個文件包含各種使用情境的範例代碼

# 載入主程序
include("src/analyze_triggered_pattern_performance_standalone_流式處理架構.jl")

# ============================================================================
# 範例 1: 批量分析多個期號
# ============================================================================

"""
批量分析指定期號範圍的所有 Gx 組合模式表現
"""
function batch_analysis_periods(start_period::Int, end_period::Int, step::Int=50)
    println("開始批量分析期號 $start_period 到 $end_period")
    
    config = get_default_config()
    results_summary = []
    
    for period in start_period:step:end_period
        println("\n正在分析期號: $period")
        
        try
            result = run_example_7(
                period, 
                config;
                min_occurrences = 5,
                min_hit_rate = 0.2,
                output_filename = "batch_period_$(period).csv"
            )
            
            push!(results_summary, (
                period = period,
                total_combinations = result.total_analyzed,
                qualified_combinations = result.filtered_count,
                success = true
            ))
            
            println("期號 $period 完成：$(result.filtered_count) 個符合條件的組合")
            
        catch e
            println("期號 $period 分析失敗：$e")
            push!(results_summary, (
                period = period,
                total_combinations = 0,
                qualified_combinations = 0,
                success = false
            ))
        end
    end
    
    # 生成摘要報告
    println("\n" * "="^60)
    println("批量分析摘要報告")
    println("-"^60)
    
    for summary in results_summary
        status = summary.success ? "✓" : "✗"
        println("期號 $(summary.period): $status $(summary.qualified_combinations) 個符合條件組合")
    end
    
    return results_summary
end

# ============================================================================
# 範例 2: 模式性能比較分析
# ============================================================================

"""
比較多個模式的歷史表現
"""
function compare_patterns(patterns::Vector{Vector{Int8}})
    println("開始比較 $(length(patterns)) 個模式的表現")
    
    config = get_default_config()
    comparison_results = []
    
    for (i, pattern) in enumerate(patterns)
        println("\n分析模式 $i: $pattern")
        
        try
            # 使用功能 3 分析模式表現
            result = run_example_3(pattern, config)
            
            push!(comparison_results, (
                pattern = pattern,
                pattern_str = "[$(join(pattern, ","))]",
                hit_rate = result.hit_rate,
                total_occurrences = result.total_occurrences,
                rank = i
            ))
            
        catch e
            println("模式 $pattern 分析失敗：$e")
        end
    end
    
    # 按出球率排序
    sort!(comparison_results, by = r -> r.hit_rate, rev = true)
    
    # 顯示比較結果
    println("\n" * "="^80)
    println("模式性能比較結果")
    println("-"^80)
    println(@sprintf("%-5s %-25s %-12s %-12s", "排名", "模式", "出球率", "觸發次數"))
    println("-"^80)
    
    for (rank, result) in enumerate(comparison_results)
        println(@sprintf("%-5d %-25s %-12.2f%% %-12d", 
            rank, result.pattern_str, result.hit_rate * 100, result.total_occurrences))
    end
    
    return comparison_results
end

# ============================================================================
# 範例 3: 自動化投注建議生成
# ============================================================================

"""
基於最佳模式生成投注建議
"""
function generate_betting_suggestions(num_suggestions::Int=5)
    println("生成前 $num_suggestions 個投注建議")
    
    config = get_default_config()
    
    # 步驟 1: 找出最佳模式
    println("步驟 1: 尋找最佳模式...")
    best_patterns = run_example_1(config)
    
    if isempty(best_patterns)
        println("未找到符合條件的模式")
        return []
    end
    
    suggestions = []
    
    # 步驟 2: 對每個最佳模式生成預測
    for (i, pattern_info) in enumerate(best_patterns[1:min(num_suggestions, length(best_patterns))])
        println("\n步驟 2.$i: 基於模式 $(pattern_info.pattern) 生成預測...")
        
        try
            prediction = run_example_5(pattern_info.pattern, config)
            
            push!(suggestions, (
                rank = i,
                pattern = pattern_info.pattern,
                pattern_hit_rate = pattern_info.hit_rate,
                recommended_gxs = prediction.matching_gxs[1:min(3, length(prediction.matching_gxs))],
                hot_numbers = prediction.hot_numbers[1:min(5, length(prediction.hot_numbers))]
            ))
            
        catch e
            println("模式 $(pattern_info.pattern) 預測失敗：$e")
        end
    end
    
    # 步驟 3: 顯示投注建議
    println("\n" * "="^80)
    println("投注建議報告")
    println("-"^80)
    
    for suggestion in suggestions
        println("建議 $(suggestion.rank):")
        println("  基於模式: $(suggestion.pattern) (歷史出球率: $(round(suggestion.pattern_hit_rate * 100, digits=2))%)")
        println("  推薦 Gx 組合: $(suggestion.recommended_gxs)")
        println("  熱門號碼: $(suggestion.hot_numbers)")
        println()
    end
    
    return suggestions
end

# ============================================================================
# 範例 4: 數據質量檢查
# ============================================================================

"""
檢查數據文件的質量和完整性
"""
function check_data_quality(data_file_path::String="data/fan5.csv")
    println("檢查數據文件質量: $data_file_path")
    
    if !isfile(data_file_path)
        println("❌ 錯誤：數據文件不存在")
        return false
    end
    
    try
        # 載入數據
        data = CSV.read(data_file_path, DataFrame, header=false)
        
        println("✓ 數據文件載入成功")
        println("  總記錄數: $(nrow(data))")
        println("  總欄位數: $(ncol(data))")
        
        # 檢查數據完整性
        missing_count = sum(ismissing.(data))
        if missing_count > 0
            println("⚠️  警告：發現 $missing_count 個缺失值")
        else
            println("✓ 數據完整，無缺失值")
        end
        
        # 檢查號碼範圍
        number_columns = data[:, 2:end]  # 跳過日期欄
        min_number = minimum(skipmissing(Matrix(number_columns)))
        max_number = maximum(skipmissing(Matrix(number_columns)))
        
        println("  號碼範圍: $min_number - $max_number")
        
        if min_number < 1 || max_number > 39
            println("⚠️  警告：號碼範圍異常")
        else
            println("✓ 號碼範圍正常")
        end
        
        # 檢查重複記錄
        unique_rows = nrow(unique(data))
        if unique_rows < nrow(data)
            println("⚠️  警告：發現 $(nrow(data) - unique_rows) 個重複記錄")
        else
            println("✓ 無重複記錄")
        end
        
        return true
        
    catch e
        println("❌ 錯誤：數據文件格式異常 - $e")
        return false
    end
end

# ============================================================================
# 範例 5: 性能測試
# ============================================================================

"""
測試不同參數設置下的程序性能
"""
function performance_test()
    println("開始性能測試...")
    
    test_configs = [
        (gx_size=2, pattern_length=8, name="小規模"),
        (gx_size=3, pattern_length=12, name="中規模"),
        (gx_size=3, pattern_length=15, name="大規模")
    ]
    
    results = []
    
    for test_config in test_configs
        println("\n測試配置: $(test_config.name)")
        println("  Gx 大小: $(test_config.gx_size)")
        println("  模式長度: $(test_config.pattern_length)")
        
        # 創建測試配置
        config = (
            data_file_path = "data/fan5.csv",
            max_number = 39,
            numbers_in_draw = 5,
            gx_size = test_config.gx_size,
            wins_pattern_length = test_config.pattern_length,
            n_future_draws = 2,
            results_dir = "results"
        )
        
        # 測試執行時間
        start_time = time()
        
        try
            result = run_example_7(
                500, 
                config;
                min_occurrences = 3,
                min_hit_rate = 0.1,
                output_filename = "performance_test_$(test_config.name).csv"
            )
            
            execution_time = time() - start_time
            
            push!(results, (
                config_name = test_config.name,
                gx_size = test_config.gx_size,
                pattern_length = test_config.pattern_length,
                execution_time = execution_time,
                total_combinations = result.total_analyzed,
                qualified_combinations = result.filtered_count,
                success = true
            ))
            
            println("  ✓ 完成時間: $(round(execution_time, digits=2)) 秒")
            println("  ✓ 分析組合數: $(result.total_analyzed)")
            println("  ✓ 符合條件組合數: $(result.filtered_count)")
            
        catch e
            execution_time = time() - start_time
            println("  ❌ 測試失敗: $e")
            
            push!(results, (
                config_name = test_config.name,
                gx_size = test_config.gx_size,
                pattern_length = test_config.pattern_length,
                execution_time = execution_time,
                total_combinations = 0,
                qualified_combinations = 0,
                success = false
            ))
        end
    end
    
    # 顯示性能測試摘要
    println("\n" * "="^80)
    println("性能測試摘要")
    println("-"^80)
    println(@sprintf("%-10s %-8s %-12s %-12s %-15s", "配置", "Gx大小", "模式長度", "執行時間(秒)", "分析組合數"))
    println("-"^80)
    
    for result in results
        status = result.success ? "✓" : "❌"
        println(@sprintf("%-10s %-8d %-12d %-12.2f %-15d", 
            result.config_name, result.gx_size, result.pattern_length, 
            result.execution_time, result.total_combinations))
    end
    
    return results
end

# ============================================================================
# 範例 6: 結果後處理和統計分析
# ============================================================================

"""
對分析結果進行統計分析
"""
function analyze_results(csv_file_path::String)
    println("分析結果文件: $csv_file_path")
    
    if !isfile(csv_file_path)
        println("❌ 錯誤：結果文件不存在")
        return nothing
    end
    
    try
        # 讀取結果
        results = CSV.read(csv_file_path, DataFrame)
        
        println("✓ 結果文件載入成功")
        println("  總記錄數: $(nrow(results))")
        
        # 基本統計
        hit_rates = results.Hit_Rate
        occurrences = results.Total_Occurrences
        
        println("\n出球率統計:")
        println("  平均值: $(round(mean(hit_rates), digits=4))")
        println("  中位數: $(round(median(hit_rates), digits=4))")
        println("  標準差: $(round(std(hit_rates), digits=4))")
        println("  最大值: $(round(maximum(hit_rates), digits=4))")
        println("  最小值: $(round(minimum(hit_rates), digits=4))")
        
        println("\n觸發次數統計:")
        println("  平均值: $(round(mean(occurrences), digits=2))")
        println("  中位數: $(median(occurrences))")
        println("  最大值: $(maximum(occurrences))")
        println("  最小值: $(minimum(occurrences))")
        
        # 分級統計
        excellent = filter(row -> row.Hit_Rate >= 0.3, results)
        good = filter(row -> 0.2 <= row.Hit_Rate < 0.3, results)
        average = filter(row -> 0.1 <= row.Hit_Rate < 0.2, results)
        poor = filter(row -> row.Hit_Rate < 0.1, results)
        
        println("\n性能分級:")
        println("  優秀 (≥30%): $(nrow(excellent)) 個 ($(round(nrow(excellent)/nrow(results)*100, digits=1))%)")
        println("  良好 (20-30%): $(nrow(good)) 個 ($(round(nrow(good)/nrow(results)*100, digits=1))%)")
        println("  一般 (10-20%): $(nrow(average)) 個 ($(round(nrow(average)/nrow(results)*100, digits=1))%)")
        println("  較差 (<10%): $(nrow(poor)) 個 ($(round(nrow(poor)/nrow(results)*100, digits=1))%)")
        
        return (
            total_count = nrow(results),
            mean_hit_rate = mean(hit_rates),
            median_hit_rate = median(hit_rates),
            std_hit_rate = std(hit_rates),
            excellent_count = nrow(excellent),
            good_count = nrow(good),
            average_count = nrow(average),
            poor_count = nrow(poor)
        )
        
    catch e
        println("❌ 錯誤：結果文件分析失敗 - $e")
        return nothing
    end
end

# ============================================================================
# 使用範例
# ============================================================================

"""
主要使用範例函數
"""
function run_examples()
    println("彩票模式分析工具 - 範例腳本演示")
    println("="^60)
    
    # 檢查數據質量
    println("\n1. 數據質量檢查")
    check_data_quality()
    
    # 性能測試
    println("\n2. 性能測試")
    performance_results = performance_test()
    
    # 模式比較
    println("\n3. 模式比較分析")
    test_patterns = [
        [0, 1, 0, 1, 0],
        [1, 0, 1, 0, 1],
        [0, 0, 1, 1, 0],
        [1, 1, 0, 0, 1]
    ]
    comparison_results = compare_patterns(test_patterns)
    
    # 生成投注建議
    println("\n4. 投注建議生成")
    betting_suggestions = generate_betting_suggestions(3)
    
    # 批量分析
    println("\n5. 批量期號分析")
    batch_results = batch_analysis_periods(400, 500, 50)
    
    println("\n" * "="^60)
    println("所有範例執行完成！")
    
    return (
        performance = performance_results,
        comparison = comparison_results,
        suggestions = betting_suggestions,
        batch = batch_results
    )
end

# 如果直接運行此腳本，執行範例
if abspath(PROGRAM_FILE) == @__FILE__
    run_examples()
end
