Think step by step, but only keep a minimum draft for each thinking step, with 5 words at most. Return the answer at the end of the response after a separator ####.

逐步思考，但每個思考步驟只保留一個最小草稿，最多 5 個單詞。在分隔符 （####） 後返回最終答案。

一步一步地思考，但將每個思考步驟限制在不超過五個字的最小草稿中。在分隔符 （####） 後返回最終答案。

“逐步 思考，但每個思考步驟只保留一個最小草稿，最多 5 個單詞。這將指導模型為每個步驟生成簡潔的基本推理。推理步驟完成後，要求模型在分隔符 （####） 後返回最終答案。這可確保最少的令牌使用，同時保持清晰度和準確性。

請根據下列問題，簡明扼要地列出解題草稿，只包含必要的資訊與步驟，避免冗長描述。
